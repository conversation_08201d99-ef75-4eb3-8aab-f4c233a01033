# AWS Image Builder Component: Configure Windows Security Registry Settings
# This component configures Windows security-related registry settings for business environments

name: windows-registry-security
description: Configure Windows security registry settings including UAC, RDP, and authentication
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureSecuritySettings
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring Windows security registry settings..."

                # Configure UAC (User Account Control) settings
                Write-Host "Configuring UAC settings for business environment..."
                $uacPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"

                # Set UAC to prompt for credentials on the secure desktop (recommended for servers)
                Set-ItemProperty -Path $uacPath -Name "ConsentPromptBehaviorAdmin" -Value 2 -Type DWord
                Set-ItemProperty -Path $uacPath -Name "ConsentPromptBehaviorUser" -Value 3 -Type DWord
                Set-ItemProperty -Path $uacPath -Name "EnableInstallerDetection" -Value 1 -Type DWord
                Set-ItemProperty -Path $uacPath -Name "EnableSecureUIAPaths" -Value 1 -Type DWord
                Set-ItemProperty -Path $uacPath -Name "EnableUIADesktopToggle" -Value 0 -Type DWord
                Set-ItemProperty -Path $uacPath -Name "EnableVirtualization" -Value 1 -Type DWord
                Set-ItemProperty -Path $uacPath -Name "PromptOnSecureDesktop" -Value 1 -Type DWord
                Set-ItemProperty -Path $uacPath -Name "ValidateAdminCodeSignatures" -Value 0 -Type DWord
                Set-ItemProperty -Path $uacPath -Name "FilterAdministratorToken" -Value 0 -Type DWord

                # Configure RDP security settings
                Write-Host "Configuring RDP security settings..."
                $rdpPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server"
                $rdpWinStationsPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp"

                # Enable RDP but with security settings
                Set-ItemProperty -Path $rdpPath -Name "fDenyTSConnections" -Value 0 -Type DWord

                # Configure RDP security level (High)
                Set-ItemProperty -Path $rdpWinStationsPath -Name "SecurityLayer" -Value 2 -Type DWord
                Set-ItemProperty -Path $rdpWinStationsPath -Name "UserAuthentication" -Value 1 -Type DWord

                # Set RDP encryption level to high
                Set-ItemProperty -Path $rdpWinStationsPath -Name "MinEncryptionLevel" -Value 3 -Type DWord

                # Configure authentication settings
                Write-Host "Configuring authentication settings..."
                $authPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"

                # Disable anonymous SID enumeration
                Set-ItemProperty -Path $authPath -Name "RestrictAnonymousSAM" -Value 1 -Type DWord
                Set-ItemProperty -Path $authPath -Name "RestrictAnonymous" -Value 1 -Type DWord

                # Configure LM authentication level (Send NTLMv2 response only)
                Set-ItemProperty -Path $authPath -Name "LmCompatibilityLevel" -Value 3 -Type DWord

                # Disable LM hash storage
                Set-ItemProperty -Path $authPath -Name "NoLMHash" -Value 1 -Type DWord

                # Configure network security settings
                Write-Host "Configuring network security settings..."
                $netPath = "HKLM:\SYSTEM\CurrentControlSet\Services\LanmanServer\Parameters"

                # Require SMB signing
                Set-ItemProperty -Path $netPath -Name "RequireSecuritySignature" -Value 1 -Type DWord
                Set-ItemProperty -Path $netPath -Name "EnableSecuritySignature" -Value 1 -Type DWord

                # Configure SMB client settings
                $smbClientPath = "HKLM:\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters"
                Set-ItemProperty -Path $smbClientPath -Name "RequireSecuritySignature" -Value 1 -Type DWord
                Set-ItemProperty -Path $smbClientPath -Name "EnableSecuritySignature" -Value 1 -Type DWord

                # Configure critical Internet Explorer security settings (high priority vulnerabilities)
                Write-Host "Configuring critical IE security settings for vulnerability mitigation..."

                # Memory protection - Data Execution Prevention (CRITICAL)
                $ieDEPPath = "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_DEP_BY_DEFAULT"
                if (-not (Test-Path $ieDEPPath)) { New-Item -Path $ieDEPPath -Force | Out-Null }
                Set-ItemProperty -Path $ieDEPPath -Name "iexplore.exe" -Value 1 -Type DWord

                $ieDEPPath64 = "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_DEP_BY_DEFAULT"
                if (-not (Test-Path $ieDEPPath64)) { New-Item -Path $ieDEPPath64 -Force | Out-Null }
                Set-ItemProperty -Path $ieDEPPath64 -Name "iexplore.exe" -Value 1 -Type DWord

                # Memory protection - Address Space Layout Randomization (HIGH)
                $ieASLRPath = "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_ASLR"
                if (-not (Test-Path $ieASLRPath)) { New-Item -Path $ieASLRPath -Force | Out-Null }
                Set-ItemProperty -Path $ieASLRPath -Name "iexplore.exe" -Value 1 -Type DWord

                $ieASLRPath64 = "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_ASLR"
                if (-not (Test-Path $ieASLRPath64)) { New-Item -Path $ieASLRPath64 -Force | Out-Null }
                Set-ItemProperty -Path $ieASLRPath64 -Name "iexplore.exe" -Value 1 -Type DWord

                # Exception handler hardening (HIGH)
                $ieExceptionPath = "HKLM:\SOFTWARE\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING"
                if (-not (Test-Path $ieExceptionPath)) { New-Item -Path $ieExceptionPath -Force | Out-Null }
                Set-ItemProperty -Path $ieExceptionPath -Name "iexplore.exe" -Value 1 -Type DWord

                $ieExceptionPath64 = "HKLM:\SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING"
                if (-not (Test-Path $ieExceptionPath64)) { New-Item -Path $ieExceptionPath64 -Force | Out-Null }
                Set-ItemProperty -Path $ieExceptionPath64 -Name "iexplore.exe" -Value 1 -Type DWord

                # Disable weak SSL/TLS protocols (CRITICAL)
                Write-Host "Disabling weak SSL/TLS protocols..."
                $ssl2ServerPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\SSL 2.0\Server"
                if (-not (Test-Path $ssl2ServerPath)) { New-Item -Path $ssl2ServerPath -Force | Out-Null }
                Set-ItemProperty -Path $ssl2ServerPath -Name "Enabled" -Value 0 -Type DWord

                $ssl3ServerPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\SSL 3.0\Server"
                if (-not (Test-Path $ssl3ServerPath)) { New-Item -Path $ssl3ServerPath -Force | Out-Null }
                Set-ItemProperty -Path $ssl3ServerPath -Name "Enabled" -Value 0 -Type DWord

                # Strong cryptography for .NET applications (HIGH)
                Write-Host "Enabling strong cryptography for .NET applications..."
                $dotNetPath = "HKLM:\SOFTWARE\Microsoft\.NETFramework\v4.0.30319"
                Set-ItemProperty -Path $dotNetPath -Name "SchUseStrongCrypto" -Value 1 -Type DWord

                $dotNetPath64 = "HKLM:\SOFTWARE\Wow6432Node\Microsoft\.NETFramework\v4.0.30319"
                Set-ItemProperty -Path $dotNetPath64 -Name "SchUseStrongCrypto" -Value 1 -Type DWord

                Write-Host "Security registry settings configured successfully"

  - name: validate
    steps:
      - name: ValidateSecurityConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating Windows security registry configuration..."

                $validationErrors = @()

                # Validate UAC settings
                try {
                    $uacPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"
                    $consentPromptAdmin = Get-ItemProperty -Path $uacPath -Name "ConsentPromptBehaviorAdmin" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty ConsentPromptBehaviorAdmin
                    if ($consentPromptAdmin -ne 2) {
                        $validationErrors += "UAC ConsentPromptBehaviorAdmin should be 2, found: $consentPromptAdmin"
                    }

                    $promptOnSecureDesktop = Get-ItemProperty -Path $uacPath -Name "PromptOnSecureDesktop" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty PromptOnSecureDesktop
                    if ($promptOnSecureDesktop -ne 1) {
                        $validationErrors += "UAC PromptOnSecureDesktop should be 1, found: $promptOnSecureDesktop"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate UAC settings: $($_.Exception.Message)"
                }

                # Validate RDP security settings
                try {
                    $rdpWinStationsPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp"
                    $securityLayer = Get-ItemProperty -Path $rdpWinStationsPath -Name "SecurityLayer" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty SecurityLayer
                    if ($securityLayer -ne 2) {
                        $validationErrors += "RDP SecurityLayer should be 2 (High), found: $securityLayer"
                    }

                    $userAuth = Get-ItemProperty -Path $rdpWinStationsPath -Name "UserAuthentication" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty UserAuthentication
                    if ($userAuth -ne 1) {
                        $validationErrors += "RDP UserAuthentication should be 1 (enabled), found: $userAuth"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate RDP security settings: $($_.Exception.Message)"
                }

                # Validate authentication settings
                try {
                    $authPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"
                    $lmCompat = Get-ItemProperty -Path $authPath -Name "LmCompatibilityLevel" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty LmCompatibilityLevel
                    if ($lmCompat -ne 3) {
                        $validationErrors += "LmCompatibilityLevel should be 3 (NTLMv2 only), found: $lmCompat"
                    }

                    $noLMHash = Get-ItemProperty -Path $authPath -Name "NoLMHash" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty NoLMHash
                    if ($noLMHash -ne 1) {
                        $validationErrors += "NoLMHash should be 1 (disabled), found: $noLMHash"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate authentication settings: $($_.Exception.Message)"
                }

                # Validate critical IE security settings
                try {
                    $ieDEPPath = "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_DEP_BY_DEFAULT"
                    $depEnabled = Get-ItemProperty -Path $ieDEPPath -Name "iexplore.exe" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty "iexplore.exe"
                    if ($depEnabled -ne 1) {
                        $validationErrors += "IE DEP should be enabled (1), found: $depEnabled"
                    }

                    $ieASLRPath = "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_ASLR"
                    $aslrEnabled = Get-ItemProperty -Path $ieASLRPath -Name "iexplore.exe" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty "iexplore.exe"
                    if ($aslrEnabled -ne 1) {
                        $validationErrors += "IE ASLR should be enabled (1), found: $aslrEnabled"
                    }

                    $ssl2ServerPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\SSL 2.0\Server"
                    $ssl2Disabled = Get-ItemProperty -Path $ssl2ServerPath -Name "Enabled" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Enabled
                    if ($ssl2Disabled -ne 0) {
                        $validationErrors += "SSL 2.0 should be disabled (0), found: $ssl2Disabled"
                    }

                    $dotNetPath = "HKLM:\SOFTWARE\Microsoft\.NETFramework\v4.0.30319"
                    $strongCrypto = Get-ItemProperty -Path $dotNetPath -Name "SchUseStrongCrypto" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty SchUseStrongCrypto
                    if ($strongCrypto -ne 1) {
                        $validationErrors += ".NET strong cryptography should be enabled (1), found: $strongCrypto"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate IE security settings: $($_.Exception.Message)"
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: Security registry settings configured correctly"
                    Write-Host "- UAC: Configured for business environment"
                    Write-Host "- RDP: High security level with NLA enabled"
                    Write-Host "- Authentication: NTLMv2 only, LM hash disabled"
                    Write-Host "- SMB: Security signing required"
                    Write-Host "- IE Security: Critical vulnerabilities mitigated (DEP, ASLR, Exception Hardening)"
                    Write-Host "- SSL/TLS: Weak protocols disabled (SSL 2.0, SSL 3.0)"
                    Write-Host "- .NET: Strong cryptography enabled"
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: Security registry configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
