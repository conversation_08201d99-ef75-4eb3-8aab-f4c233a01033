# Example AWS ImageBuilder Recipe with AWS CLI Check and S3 Applications Copy
# This recipe ensures AWS CLI is available before copying applications

name: WindowsServer2022WithS3ApplicationsAndCLI
description: Windows Server 2022 base image with AWS CLI verification and applications copied from S3
schemaVersion: 1.0
version: 1.0.0

# Base image - Windows Server 2022 (automatically uses latest)
parentImage: Windows_Server-2022-English-Full-Base

# Build and test components
components:
# AWS managed component for Windows updates
- name: update-windows
  parameters:
  - name: exclude
    value:
    - "KB5005463" # Example: exclude specific updates if needed
  - name: include
    value: []

# Install .NET Framework 4.8 (if needed for your applications)
- name: install-dotnet48
  parameters: []

# Ensure AWS CLI is available (safety measure)
- name: ensure-aws-cli
  parameters: []

# Copy applications from S3 bucket (simplified version)
- name: windows-applications-s3-simple
  parameters: []

# Additional components can be added here
# - name: windows-tools-bginfo
#   parameters: []

# Test phase - verify the applications were copied
- name: test-phase
  parameters: []

# Working directory for the build
workingDirectory: /tmp

# Additional metadata
tags:
  Environment: Production
  Component: S3Applications
  CreatedBy: ImageBuilder
