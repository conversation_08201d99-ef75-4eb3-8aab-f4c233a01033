
#Requires -Version 5.1
#Requires -Modules ImportExcel

<#
.SYNOPSIS
    Configuration Management Script for Windows and Linux OS deployment
.DESCRIPTION
    Handles OS configuration tasks including account setup, disk configuration,
    and application installations for automated server deployment
.NOTES
    Version: 2.0
    PowerShell Version: 5.1+
    Author: Auto Deploy Team
#>

[CmdletBinding()]
param()

### Global Variables ###
$script:currentPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$script:config = $null
$script:buildConfigData = $null

# Initialize configuration with error handling
try {
    $script:config = Get-Content -Path "$script:currentPath\scriptConfigs.json" -ErrorAction Stop | ConvertFrom-Json
    Write-Verbose "Configuration loaded successfully"
}
catch {
    Write-Error "Failed to load configuration: $($_.Exception.Message)"
    exit 1
}

### Import modules with error handling ###
try {
    Import-Module $script:config.AutoDeployModule -ErrorAction Stop
    Import-Module $script:config.ADDMModule -ErrorAction Stop
    Import-Module ImportExcel -ErrorAction Stop
    Write-Verbose "Modules imported successfully"
}
catch {
    Write-Error "Failed to import required modules: $($_.Exception.Message)"
    exit 1
}

### Paths ###
$script:appSource = $script:config.AppSourcePath
$script:credPath = $script:config.CredPath
$script:cliCredsPath = Join-Path $script:credPath "cliCreds.xml"
$script:adminCredLocation = Join-Path $script:credPath "windowsCreds.xml"
$script:mudCreds = Join-Path $script:credPath "prdDomainCreds.xml"
$script:ppeCreds = Join-Path $script:credPath "ppeDomainCreds.xml"
$script:devCreds = Join-Path $script:credPath "devDomainCreds.xml"

### Constants ###
$script:JOB_STAGE = "OS_CONFIG"
$script:STATUS_COMPLETED = "COMPLETED"
$script:STATUS_IN_PROGRESS = "IN_PROGRESS"
$script:STATUS_PENDING = "PENDING"

### Script-level variables ###
$script:configSuccess = $false
$script:finalResponse = ""
$script:configRequired = $false
$script:backupRequired = $false
$script:monitoringRequired = $false

### CodeRefs for configs ###
$localAdminDefault = "Administrator"
$correctAdminName = "adminacc"
$mudADGroups = "mudadGroups"
$devADGroups = "devadGroups"
$ppeADGroups = "ppeadGroups"
$mudsuffixList = "muddnsList"
$devsuffixList = "devdnsList"
$ppesuffixList = "ppednsList"
$mudMSName = "mudSCOMserver"
$devMSName = "devSCOMserver"
$ppeMSName = "ppeSCOMserver"
$mudMSGroup = "mudSCOMGroup"
$mudSQLGroup = "mudSCOMSQLGroup"
$devMSGroup = "devSCOMGroup"
$devSQLGroup = "devSCOMSQLGroup"
$ppeMSGroup = "ppeSCOMGroup"
$ppeSQLGroup = "ppeSCOMSQLGroup"
$mudMSPort = "mudSCOMPort"
$devMSPort = "devSCOMPort"
$ppeMSPort = "ppeSCOMPort"
$sqlBlock = "sqlBlock"
$sharedBlock = "ndbBlock"
$ppCode = "ppCode"
$timeCode = "timezone"

### Competencies ###
$script:bcxLinuxComp = $script:config.BCXLinuxCompetency

### Function to load and cache build configuration data ###
function Get-BuildConfigData {
    [CmdletBinding()]
    param()

    if ($null -eq $script:buildConfigData) {
        try {
            Write-Verbose "Loading build configuration data from Excel file"
            $script:buildConfigData = Import-Excel -Path $script:config.BuildConfigsPath -WorksheetName "OS" -ErrorAction Stop
            Write-Verbose "Build configuration data loaded successfully"
        }
        catch {
            Write-Error "Failed to load build configuration data: $($_.Exception.Message)"
            throw
        }
    }
    return $script:buildConfigData
}

### Function to get configuration value by CodeRef ###
function Get-ConfigValue {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$CodeRef,

        [switch]$AsList
    )

    $configData = Get-BuildConfigData
    $value = ($configData | Where-Object { $_.CodeRef -eq $CodeRef }).Details

    if ($AsList -and $value) {
        return $value.Split(";")
    }

    return $value
}

### Load configuration values ###
$script:mudAdminGroups = Get-ConfigValue -CodeRef $mudADGroups
$script:devAdminGroups = Get-ConfigValue -CodeRef $devADGroups
$script:ppeAdminGroups = Get-ConfigValue -CodeRef $ppeADGroups

$script:mudSuffixSearchList = Get-ConfigValue -CodeRef $mudsuffixList -AsList
$script:devSuffixSearchList = Get-ConfigValue -CodeRef $devsuffixList -AsList
$script:ppeSuffixSearchList = Get-ConfigValue -CodeRef $ppesuffixList -AsList

$script:mudSCOMServer = Get-ConfigValue -CodeRef $mudMSName
$script:devSCOMServer = Get-ConfigValue -CodeRef $devMSName
$script:ppeSCOMServer = Get-ConfigValue -CodeRef $ppeMSName
$script:mudSCOMServerGroup = Get-ConfigValue -CodeRef $mudMSGroup
$script:mudSCOMServerSQLGroup = Get-ConfigValue -CodeRef $mudSQLGroup
$script:devSCOMServerGroup = Get-ConfigValue -CodeRef $devMSGroup
$script:devSCOMServerSQLGroup = Get-ConfigValue -CodeRef $devSQLGroup
$script:ppeSCOMServerGroup = Get-ConfigValue -CodeRef $ppeMSGroup
$script:ppeSCOMServerSQLGroup = Get-ConfigValue -CodeRef $ppeSQLGroup
$script:mudSCOMServerPort = Get-ConfigValue -CodeRef $mudMSPort
$script:devSCOMServerPort = Get-ConfigValue -CodeRef $devMSPort
$script:ppeSCOMServerPort = Get-ConfigValue -CodeRef $ppeMSPort

$script:localAdminCorrect = Get-ConfigValue -CodeRef $correctAdminName
$script:sqlBlockSize = Get-ConfigValue -CodeRef $sqlBlock
$script:sharedBlockSize = Get-ConfigValue -CodeRef $sharedBlock
$script:powerplan = Get-ConfigValue -CodeRef $ppCode
$script:time = Get-ConfigValue -CodeRef $timeCode

### Helper function for disk configuration ###
function Get-DiskConfiguration {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$BuildSpecs,

        [Parameter(Mandatory = $true)]
        [string]$App
    )

    # Parse disk information
    $tier2disks = ($BuildSpecs.t2storage).Split(",") | Where-Object { $_.Length -gt 0 }
    $tier2drives = ($BuildSpecs.t2drives).Split(",") | Where-Object { $_.Length -gt 0 }
    $tier3disks = ($BuildSpecs.t3storage).Split(",") | Where-Object { $_.Length -gt 0 }
    $tier3drives = ($BuildSpecs.t3drives).Split(",") | Where-Object { $_.Length -gt 0 }

    # Determine block size based on application type
    $blockSize = if ($App -match "SQL") {
        $script:sqlBlockSize
    } else {
        $script:sharedBlockSize
    }

    $diskDetails = @()

    # Process Tier 2 disks
    if ($tier2drives.Count -gt 0) {
        for ($i = 0; $i -lt $tier2drives.Count; $i++) {
            if ($tier2drives[$i] -and $tier2drives[$i].Trim()) {
                $diskDetails += [PSCustomObject]@{
                    DiskLetter = $tier2drives[$i].Trim()
                    DiskSize   = if ($i -lt $tier2disks.Count) { $tier2disks[$i] } else { $tier2disks[0] }
                    BlockSize  = $blockSize
                }
            }
        }
    }

    # Process Tier 3 disks
    if ($tier3drives.Count -gt 0) {
        for ($i = 0; $i -lt $tier3drives.Count; $i++) {
            if ($tier3drives[$i] -and $tier3drives[$i].Trim()) {
                $diskDetails += [PSCustomObject]@{
                    DiskLetter = $tier3drives[$i].Trim()
                    DiskSize   = if ($i -lt $tier3disks.Count) { $tier3disks[$i] } else { $tier3disks[0] }
                    BlockSize  = $blockSize
                }
            }
        }
    }

    return $diskDetails
}

### Helper function to safely remove PSDrives ###
function Remove-PSDriveSafely {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string[]]$DriveNames
    )

    foreach ($driveName in $DriveNames) {
        try {
            $drive = Get-PSDrive -Name $driveName -ErrorAction SilentlyContinue
            if ($drive) {
                Remove-PSDrive -Name $driveName -Force -ErrorAction Stop
                Write-Verbose "Successfully removed PSDrive: $driveName"
            }
        }
        catch {
            Write-Warning "Failed to remove PSDrive '$driveName': $($_.Exception.Message)"
        }
    }
}

function Set-ServerAccounts {
    <#
    .SYNOPSIS
        Configures server accounts and administrator groups
    .DESCRIPTION
        Sets up local administrator account and adds domain groups to local administrators
    .PARAMETER ServerName
        Target server FQDN
    .PARAMETER Credentials
        Credentials for remote connection
    .PARAMETER AccountsToAdd
        List of accounts/groups to add to local administrators
    #>
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$ServerName,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$Credentials,

        [Parameter(Mandatory = $true)]
        [string[]]$AccountsToAdd
    )

    try {
        Write-Verbose "Configuring server accounts for $ServerName"

        # Configure local administrator account (using original working logic)
        try {
            $adminPass = Import-Clixml -Path $script:adminCredLocation

            Invoke-Command -ComputerName $ServerName -Credential $Credentials -ErrorAction Stop -ScriptBlock {
                param(
                    $adminPass,
                    $localAdminDefault,
                    $localAdminCorrect
                )
                $administrator = Get-LocalUser -Name $localAdminDefault -ErrorAction SilentlyContinue
                if ($administrator) {
                    $administrator | Set-LocalUser -Name $localAdminCorrect
                }
                else {
                    $administrator = Get-LocalUser -Name $localAdminCorrect
                }
                $administrator | Set-LocalUser -Password $adminPass.Password -PasswordNeverExpires $true
            } -ArgumentList $adminPass, $localAdminDefault, $script:localAdminCorrect

            Write-Verbose "Local administrator account configured successfully"
        }
        catch {
            Write-Warning "Failed to configure local administrator account: $($_.Exception.Message)"
        }

        # Configure administrator groups
        Invoke-Command -ComputerName $ServerName -Credential $Credentials -ErrorAction Stop -ScriptBlock {
            param($svcAccounts)

            try {
                $existingGroups = Get-LocalGroup -Name "Administrators" | Get-LocalGroupMember | Select-Object -ExpandProperty Name
                $groupsToAdd = Compare-Object -ReferenceObject $svcAccounts -DifferenceObject $existingGroups |
                              Where-Object { $_.SideIndicator -eq "<=" } |
                              Select-Object -ExpandProperty InputObject

                if ($groupsToAdd) {
                    foreach ($group in $groupsToAdd) {
                        try {
                            Add-LocalGroupMember -Group "Administrators" -Member $group -ErrorAction Stop
                            Write-Verbose "Added $group to Administrators group"
                        }
                        catch {
                            Write-Warning "Failed to add $group to Administrators group: $($_.Exception.Message)"
                        }
                    }
                }
            }
            catch {
                throw "Failed to configure administrator groups: $($_.Exception.Message)"
            }
        } -ArgumentList $AccountsToAdd

        Write-Verbose "Administrator groups configured successfully"
        return $true
    }
    catch {
        Write-Error "Failed to configure server accounts: $($_.Exception.Message)"
        return $false
    }
}

function Start-ConfigManagement {
    <#
    .SYNOPSIS
        Main configuration management function
    .DESCRIPTION
        Processes pending OS configuration jobs and executes appropriate configuration tasks
    #>
    [CmdletBinding()]
    param()

    try {
        Write-Verbose "Starting configuration management process"

        # Search for pending jobs (empty string for all hosting platforms)
        $hostingPlatform = ""
        $apiInputData = Get-Job -JobID "" -JobStatus $script:STATUS_PENDING -JobStage $script:JOB_STAGE -HostingPlatform $hostingPlatform

        if (-not $apiInputData) {
            Write-Verbose "No pending jobs found"
            return
        }

        # Update job status to in progress
        try {
            $updateParams = @{
                JobID = $apiInputData.request_jobid
                JobStage = $script:JOB_STAGE
                JobStatus = $script:STATUS_IN_PROGRESS
                SuccessStatus = $true
                LogMessage = "OS Config Started"
                LogType = "INFO"
                IssueRef = " "
                IssueType = " "
            }
            Update-Job @updateParams
            Write-Verbose "Job status updated to IN_PROGRESS"
        }
        catch {
            Write-Error "Failed to update job status to IN_PROGRESS: $($_.Exception.Message)"
            throw "Cannot proceed without updating job status"
        }

        # Initialize response variables
        $script:finalResponse = ""
        $configSuccess = $false
        $addmStarted = $false

        # Process based on OS type
        if ($apiInputData.os_version -match "Windows Server") {
            Write-Verbose "Processing Windows Server configuration"
            $osConfig = Invoke-WindowsOsConfigs -buildSpecs $apiInputData
            $configSuccess = $osConfig.Succeeded
            $script:finalResponse = $osConfig.Message
            $addmStarted = Start-ADDMScan -specInfo $apiInputData
        }
        elseif ($apiInputData.competency -eq $script:bcxLinuxComp) {
            Write-Verbose "Processing BCX Linux configuration"
            $osConfig = BCXLinuxOsConfigs -buildSpecs $apiInputData
            $configSuccess = $osConfig.Succeeded
            $script:finalResponse = $osConfig.Message
            $addmStarted = Start-ADDMScan -specInfo $apiInputData
        }
        else {
            Write-Verbose "OS configuration not required for this system type"
            $configSuccess = $true
            $addmStarted = $true
            $script:finalResponse = "OS Config Not Required"
        }

        # Append ADDM scan status
        if ($addmStarted) {
            $script:finalResponse += ", ADDM scan started successfully"
        }
        else {
            $script:finalResponse += ", ADDM scan failed to start"
        }

        # Update final job status
        try {
            $finalUpdateParams = @{
                JobID = $apiInputData.request_jobid
                JobStage = $script:JOB_STAGE
                JobStatus = $script:STATUS_COMPLETED
                SuccessStatus = $configSuccess
                LogMessage = $script:finalResponse
                LogType = "INFO"
                IssueRef = " "
                IssueType = "OS_CONFIG"
            }
            Update-Job @finalUpdateParams
            Write-Verbose "Final job status updated successfully: Success=$configSuccess"
        }
        catch {
            Write-Error "Failed to update final job status: $($_.Exception.Message)"
            # This is critical - if we can't update job status, we should still throw
            throw "Failed to update job completion status"
        }

        Write-Verbose "Configuration management process completed"
    }
    catch {
        $errorMessage = "Configuration management failed: $($_.Exception.Message)"
        Write-Error $errorMessage

        # Critical: Update job status to indicate failure
        if ($apiInputData) {
            try {
                $failureUpdateParams = @{
                    JobID = $apiInputData.request_jobid
                    JobStage = $script:JOB_STAGE
                    JobStatus = $script:STATUS_COMPLETED
                    SuccessStatus = $false
                    LogMessage = $errorMessage
                    LogType = "ERROR"
                    IssueRef = "SCRIPT_EXCEPTION"
                    IssueType = "OS_CONFIG"
                }
                Update-Job @failureUpdateParams
                Write-Verbose "Job status updated to indicate failure"
            }
            catch {
                Write-Error "Failed to update job status on error: $($_.Exception.Message)"
            }
        }
        throw
    }
}

function Invoke-WindowsOsConfigs {
    <#
    .SYNOPSIS
        Configures Windows Server OS settings
    .DESCRIPTION
        Performs comprehensive Windows Server configuration including accounts, disks, networking, and applications
    .PARAMETER buildSpecs
        Build specifications object containing server configuration details
    #>
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$buildSpecs
    )
    
    try {
        Write-Verbose "Initializing Windows OS configuration variables"

        # Initialize basic variables
        $serverName = $buildSpecs.vm_name
        $fqdn = "{0}.{1}" -f $serverName, $buildSpecs.dns_suffix
        $serverDescription = $buildSpecs.vm_description
        $app = $buildSpecs.app_type
        $sla = $buildSpecs.environment
        $osVersion = $buildSpecs.os_version

        # Determine environment-specific settings
        $envConfig = switch -Wildcard ($buildSpecs.dns_suffix) {
            "dev*" {
                @{
                    Credentials = Import-Clixml -Path $script:devCreds -ErrorAction Stop
                    AdminGroups = $script:devAdminGroups
                    DNSSearchList = $script:devSuffixSearchList
                    SCOMServer = $script:devSCOMServer
                    SCOMServerGroup = $script:devSCOMServerGroup
                    SCOMServerSQLGroup = $script:devSCOMServerSQLGroup
                    SCOMPort = $script:devSCOMServerPort
                }
            }
            "ppe*" {
                @{
                    Credentials = Import-Clixml -Path $script:ppeCreds -ErrorAction Stop
                    AdminGroups = $script:ppeAdminGroups
                    DNSSearchList = $script:ppeSuffixSearchList
                    SCOMServer = $script:ppeSCOMServer
                    SCOMServerGroup = $script:ppeSCOMServerGroup
                    SCOMServerSQLGroup = $script:ppeSCOMServerSQLGroup
                    SCOMPort = $script:ppeSCOMServerPort
                }
            }
            default {
                @{
                    Credentials = Import-Clixml -Path $script:mudCreds -ErrorAction Stop
                    AdminGroups = $script:mudAdminGroups
                    DNSSearchList = $script:mudSuffixSearchList
                    SCOMServer = $script:mudSCOMServer
                    SCOMServerGroup = $script:mudSCOMServerGroup
                    SCOMServerSQLGroup = $script:mudSCOMServerSQLGroup
                    SCOMPort = $script:mudSCOMServerPort
                }
            }
        }

        # Set SCOM group based on application type
        $scomGroup = if ($app -match 'SQL') {
            $envConfig.SCOMServerSQLGroup
        } else {
            $envConfig.SCOMServerGroup
        }

        # Determine requirements based on SLA and app type
        $backupRequired = $sla -like "prd"
        $monitoringRequired = $app -notmatch "POC"

        Write-Verbose "Environment configuration loaded for $($buildSpecs.dns_suffix)"

        # Process disk configuration
        Write-Verbose "Processing disk configuration"
        $diskDetails = Get-DiskConfiguration -BuildSpecs $buildSpecs -App $app

        # Execute configuration functions
        Write-Verbose "Starting OS configuration tasks"

        # Critical functions with return values
        $accounts = Set-ServerAccounts -ServerName $fqdn -Credentials $envConfig.Credentials -AccountsToAdd $envConfig.AdminGroups

        # Non-critical configuration functions
        ConfigNics -servername $fqdn -user $envConfig.Credentials -dnsList $envConfig.DNSSearchList
        ConfigIdentity -serverName $fqdn -user $envConfig.Credentials -description $serverDescription -time $script:time -powerplan $script:powerplan
        ConfigDisks -serverName $fqdn -user $envConfig.Credentials -diskinfo $diskDetails -osSize $buildSpecs.os_disk
        ConfigDesktop -serverName $fqdn -user $envConfig.Credentials -sla $sla
        ConfigSCCM -serverName $fqdn -user $envConfig.Credentials
        ConfigCrowdStrike -serverName $fqdn -user $envConfig.Credentials

        # Optional components based on requirements
        if ($backupRequired) {
            ConfigBackupAgent -serverName $serverName
        }

        if ($monitoringRequired) {
            ConfigMonitoringAgent -serverName $fqdn -user $envConfig.Credentials -mgmtServerFQDN $envConfig.SCOMServer -mgmtServerGroup $scomGroup -mgmtServerPort $envConfig.SCOMPort -osVersion $osVersion
        }

        # Server restart
        $restartServer = ServerReboot -serverName $fqdn -user $envConfig.Credentials

        # Determine final status
        if ($accounts) {
            $critCompleted = $true
            $message = if ($restartServer) {
                "OS Config Complete"
            } else {
                "OS Config Complete - Server Reboot still required"
            }
        }
        else {
            $critCompleted = $false
            $message = "OS Config Failed To Access Server - possible redeploy required"
        }

        Write-Verbose "Windows OS configuration completed with status: $message"
    }
    catch {
        Write-Error "Windows OS configuration failed: $($_.Exception.Message)"
        $critCompleted = $false
        $message = "OS Config Failed: $($_.Exception.Message)"
    }

    # Return result object
    return [PSCustomObject]@{
        Succeeded = $critCompleted
        Message = $message
    }
}

Function ConfigNics {
    param(
        [Parameter(Mandatory = $true)]$servername,
        [Parameter(Mandatory = $true)]$user,
        [Parameter(Mandatory = $true)]$dnsList
    )

    Try {
        Invoke-Command -ComputerName $servername -Credential $user -ErrorAction Stop -Scriptblock {
            param(
                $suffixSearchList
            )
            Get-NetAdapter | ForEach-Object { Disable-NetAdapterBinding -InterfaceAlias $_.Name -ComponentID ms_tcpip6 }
            Set-DnsClientGlobalSetting -SuffixSearchList $suffixSearchList
        } -ArgumentList $dnsList
    }
    catch {
        ### Do Nothing ###
    }
}

Function ConfigIdentity {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$serverName,
        [Parameter(Mandatory = $true)]$user,
        [Parameter(Mandatory = $true)]$description,
        [Parameter(Mandatory = $true)]$time,
        [Parameter(Mandatory = $true)]$powerplan
    )

    try {

        ### set server description ###
        $value = @{Description = "$description" }
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {
            param(
                $value
            )
            Set-CimInstance -Query 'Select * From Win32_OperatingSystem' -Property $value -ErrorAction Stop
        } -ArgumentList $value        

        ### set correct timezone ###
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {
            param(
                $timezone
            )
            Set-TimeZone -Name $timezone
        } -ArgumentList $time
    
        ### set power plan ###
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {
            param(
                $powerplan
            )
            powercfg.exe -SETACTIVE $powerplan
        } -ArgumentList $powerplan

    }
    catch {
        ### Do Nothing ###
    }

}

Function ConfigDisks {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$serverName,
        [Parameter(Mandatory = $true)]$user,
        [Parameter(Mandatory = $true)]$diskinfo,
        [Parameter(Mandatory = $true)]$osSize
    )

    $serverName
    $diskinfo = $diskinfo | Where-Object { $_.DiskLetter.length -gt 0 }
    
    Try {
        ### set dvd rom to letter Z:\ ###
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {
            $DVD_Drive = Get-WmiObject win32_volume -EnableAllPrivileges -filter 'DriveType = "5"'    
            $DVD_Drive.DriveLetter = "Z:"    
            $DVD_Drive.Put() | Out-Null            
        }

        ### Expand C: to max size ###
        $cSize = Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock { (Get-Disk | Where-Object { $_.Number -eq 0 } | Select-Object -ExpandProperty AllocatedSize) / 1gb }
        if ($cSize -ne $osSize) {
            Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {
                $size = Get-PartitionSupportedSize -DriveLetter C
                Resize-Partition -DriveLetter C -Size $size.Sizemax
            }
        }
        

        ### initialize, online, format disk partitions ###
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {
            param(
                $diskinfo
            )
            Get-Disk | Where-Object { $_.PartitionStyle -match "RAW" } | Initialize-Disk -PartitionStyle GPT -ErrorAction Stop   
            for ($i = 0; $i -lt ($diskinfo | Measure-Object).count; $i++) {        
                New-Partition -DiskNumber ($i + 1) -DriveLetter ($diskinfo[$i].DiskLetter).replace(" ", "") -UseMaximumSize -ErrorAction Stop
                Format-Volume -DriveLetter ($diskinfo[$i].DiskLetter).replace(" ", "") -NewFileSystemLabel "New Volume" -FileSystem NTFS -AllocationUnitSize $diskinfo[$i].blockSize -ErrorAction Stop   
            }
        } -ArgumentList $diskinfo

    }
    catch {
        ### Do Nothing ###
    }

}

Function ConfigDesktop {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$serverName,
        [Parameter(Mandatory = $true)]$user,
        [Parameter(Mandatory = $true)]$sla
    )

    if ($sla -match "dev|poc") {
        $envSLA = "DEV"
    }
    elseif ($sla -match "ppe") {
        $envSLA = "PPE"
    }
    elseif ($sla -like "prd") {
        $envSLA = "PROD"
    }

    # Define source and destination paths
    $sourcePath = "$appSource\DeskTopInfo"
    $destinationPath = "\\$serverName\C$\Temp"
    
    try {

        # Create a PSDrive for the source server
        New-PSDrive -Name "SourceServer" -PSProvider FileSystem -Root $sourcePath
        # Create a PSDrive for the destination server
        New-PSDrive -Name "DestinationServer" -PSProvider FileSystem -Root $destinationPath -Credential $user

        $drivePath = "DestinationServer:\ServerInstalls\DesktopInfo"
        if (Test-Path $drivePath) {
            Remove-Item $drivePath -Recurse -Force -ErrorAction Stop
        }

        Copy-Item -path "SourceServer:\" -Destination $drivePath -Recurse -ErrorAction Stop

        # Safely remove PSDrives
        Remove-PSDriveSafely -DriveNames @("SourceServer", "DestinationServer")
    
        Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {
            param(
                $envSLA
            )

            if (Test-Path "C:\DesktopInfo") {                
                Remove-Item "C:\DesktopInfo" -Recurse -Force
            }

            Set-Location C:\temp\ServerInstalls\DeskTopInfo -ErrorAction Stop
            # Run BGInfo installation and suppress success messages
            $null = & C:\temp\ServerInstalls\DeskTopInfo\INSTALL_BGinfo_$envSLA.bat 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Verbose "BGInfo installation completed successfully"
            } else {
                Write-Error "BGInfo installation failed with exit code: $LASTEXITCODE"
            }
            Copy-Item C:\temp\ServerInstalls\DeskTopInfo\BGinfo_$envSLA.lnk -Destination "C:\ProgramData\Microsoft\Windows\Start Menu\Programs\StartUp" -Force -Confirm:$false -ErrorAction Stop
        } -ArgumentList $envSLA

    }
    catch {
        ### Do Nothing ###
    }
}

Function ConfigSCCM {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$serverName,
        [Parameter(Mandatory = $true)]$user

    )
    # Define source and destination paths
    $sourcePath = "$appSource\SCCM"
    $destinationPath = "\\$serverName\C$\Temp"
    
    try {

        # Create a PSDrive for the source server
        New-PSDrive -Name "SourceServer" -PSProvider FileSystem -Root $sourcePath
        # Create a PSDrive for the destination server
        New-PSDrive -Name "DestinationServer" -PSProvider FileSystem -Root $destinationPath -Credential $user

        $drivePath = "DestinationServer:\ServerInstalls\SCCM"
        if (Test-Path $drivePath) {
            Remove-Item $drivePath -Recurse -Force -ErrorAction Stop
        }

        Copy-Item -path "SourceServer:\" -Destination $drivePath -Recurse -ErrorAction Stop

        # Safely remove PSDrives
        Remove-PSDriveSafely -DriveNames @("SourceServer", "DestinationServer")
    
        $installerPath = "C:\temp\ServerInstalls\SCCM\ccmsetup.exe"
        Invoke-Command -ComputerName $serverName -Credential $user  -ScriptBlock {    
            param (
                $installerPath
            )        
            # Start the SCCM client installation
            $process = Start-Process -FilePath "cmd.exe" -ArgumentList "/c $installerPath" -Wait -PassThru -NoNewWindow
            if ($process.ExitCode -eq 0) {
                Write-Verbose "SCCM client installation completed successfully"
            } elseif ($process.ExitCode -eq 1638) {
                Write-Verbose "SCCM client already installed - deployment successful"
            } else {
                Write-Warning "SCCM client installation completed with exit code: $($process.ExitCode)"
            }
        } -ArgumentList $installerPath
        Start-Sleep 60
    }
    catch {
        ### Do Nothing ###
    }

}

Function ConfigCrowdStrike {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$serverName,
        [Parameter(Mandatory = $true)]$user
    )
    
    # Define source and destination paths
    $sourcePath = "$appSource\CrowdStrike"
    $destinationPath = "\\$serverName\C$\Temp"
    
    try {

        # Create a PSDrive for the source server
        New-PSDrive -Name "SourceServer" -PSProvider FileSystem -Root $sourcePath
        # Create a PSDrive for the destination server
        New-PSDrive -Name "DestinationServer" -PSProvider FileSystem -Root $destinationPath -Credential $user

        $drivePath = "DestinationServer:\ServerInstalls\CrowdStrike"
        if (Test-Path $drivePath) {
            Remove-Item $drivePath -Recurse -Force -ErrorAction Stop
        }

        Copy-Item -path "SourceServer:\" -Destination $drivePath -Recurse -ErrorAction Stop

        # Safely remove PSDrives
        Remove-PSDriveSafely -DriveNames @("SourceServer", "DestinationServer")
    
        $installerPath = "C:\Temp\ServerInstalls\CrowdStrike\Install_ADS.bat"
        Invoke-Command -ComputerName $serverName -Credential $user  -ScriptBlock {    
            param (
                $installerPath
            )        
            # Start the CrowdStrike installation
            $process = Start-Process -FilePath "cmd.exe" -ArgumentList "/c $installerPath" -Wait -PassThru -NoNewWindow
            if ($process.ExitCode -eq 0) {
                Write-Verbose "CrowdStrike installation completed successfully"
            } elseif ($process.ExitCode -eq 1638) {
                Write-Verbose "CrowdStrike already installed - deployment successful"
            } else {
                Write-Warning "CrowdStrike installation completed with exit code: $($process.ExitCode)"
            }
        } -ArgumentList $installerPath
        Start-Sleep -Seconds 60
    }
    catch {
        ### Do Nothing ###
    }
}

Function ConfigBackupAgent {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$serverName
    )
    $serverName
    "Install Backup Agent"
}

Function ConfigMonitoringAgent {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$serverName,
        [Parameter(Mandatory = $true)]$user,
        [Parameter(Mandatory = $true)]$mgmtServerFQDN,
        [Parameter(Mandatory = $true)]$mgmtServerGroup,
        [Parameter(Mandatory = $true)]$mgmtServerPort,
        [Parameter(Mandatory = $true)]$osVersion
    )

    # Define source and destination paths
    $sourcePath = "$appSource\SCOM\Agent"
    $destinationPath = "\\$serverName\C$\Temp"
    
    try {

        # Create a PSDrive for the source server
        New-PSDrive -Name "SourceServer" -PSProvider FileSystem -Root $sourcePath
        # Create a PSDrive for the destination server
        New-PSDrive -Name "DestinationServer" -PSProvider FileSystem -Root $destinationPath -Credential $user

        $drivePath = "DestinationServer:\ServerInstalls\SCOM"
        if (Test-Path $drivePath) {
            Remove-Item $drivePath -Recurse -Force -ErrorAction Stop
        }

        Copy-Item -path "SourceServer:\" -Destination $drivePath -Recurse -ErrorAction Stop

        # Safely remove PSDrives
        Remove-PSDriveSafely -DriveNames @("SourceServer", "DestinationServer")

        # Specify the path to the SCOM client installer MSI file
        $installerPath = "C:\temp\ServerInstalls\SCOM\AMD64\MOMAgent.msi"
        # Specify the SCOM management server and other properties
        $msFQDN = $mgmtServerFQDN
        $msGName = $mgmtServerGroup
        $msPort = $mgmtServerPort
        $additionalProperties = "/qn USE_SETTINGS_FROM_AD=0 USE_MANUALLY_SPECIFIED_SETTINGS=1 MANAGEMENT_GROUP=$msGName MANAGEMENT_SERVER_DNS=$msFQDN SECURE_PORT=$msPort ACTIONS_USE_COMPUTER_ACCOUNT=1 AcceptEndUserLicenseAgreement=1"
        if ($osVersion -match "2019") {
            $removeKey = $true
        }
        else {
            $removeKey = $false
        }

        Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {
            param (
                $installerPath,
                $additionalProperties,
                $removeKey
            )
            if ($removeKey) {
                $registryKeyPath = "HKLM:\SYSTEM\CurrentControlSet\Services\EventLog\Security"
                # Check if the registry key exists before attempting to delete it
                if (Test-Path -Path $registryKeyPath) {
                    # Remove the registry key and its subkeys recursively
                    Remove-ItemProperty -Path $registryKeyPath -Name CustomSD -Force -Verbose -ErrorAction SilentlyContinue
                }
            }
            # Install SCOM client with specified properties
            $process = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i $installerPath $additionalProperties" -Wait -PassThru -NoNewWindow
            if ($process.ExitCode -eq 0) {
                Write-Verbose "SCOM agent installation completed successfully"
            } elseif ($process.ExitCode -eq 1638) {
                Write-Verbose "SCOM agent already installed - deployment successful"
            } else {
                Write-Warning "SCOM agent installation completed with exit code: $($process.ExitCode)"
            }
        } -ArgumentList $installerPath, $additionalProperties, $removeKey

        Start-Sleep -Seconds 60
    }
    catch {
        ### Do Nothing ###
    }
}

Function ServerReboot {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$serverName,
        [Parameter(Mandatory = $true)]$user
    )

    try {
        "Sleep 5 minutes while waiting for applications to finish installing"
        Start-Sleep -Seconds 300
        Restart-Computer -ComputerName $serverName -Credential $user -Force -ErrorAction Stop
        Start-Sleep -Seconds 60
        $restartSuccess = $true
    }
    catch {
        $restartSuccess = $false
    }
    $restartSuccess
}

Function BCXLinuxOsConfigs {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$buildSpecs
    )

    $cliCreds = Import-Clixml -Path $script:cliCredsPath
    $vCenterSuccess = vCenter_Connection -vCenterName $buildSpecs.vc_node -credentials $cliCreds
    $vmName = $buildSpecs.vm_name
    $client = $buildSpecs.business_unit
    if ($buildSpecs.environment -match "prd") {
        $env = "prod"
    }
    else {
        $env = "nonprod"
    }

    if ($buildSpecs.business_unit -match "Santam") {
        $client = "santam"
    }
    else {
        $client = "nonsantam"
    }

    if ($vCenterSuccess) {   
        try {
            if ($buildSpecs.os_version -match "RHEL8") {                
                $command = "wget -O- --no-check-certificate https://linuxmanager.sanlam.co.za/cloud/deployment/PostDeployConfig8.sh | /bin/bash -s -- -c $client -s $env -d data -m mail -p Y"
                $command.Replace("&client&", $client)
                $command.Replace("&env&", $env)
                Invoke-VMScript -VM $vmName -ScriptText $command -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null    
            }
            elseif ($buildSpecs.os_version -match "RHEL9") {
                $command = "wget -O- --no-check-certificate https://linuxmanager.sanlam.co.za/cloud/deployment/PostDeployConfig9.sh | /bin/bash -s -- -c $client -s $env -d data -m mail -p Y"
                $command.Replace("&client&", $client)
                $command.Replace("&env&", $env)
                Invoke-VMScript -VM $vmName -ScriptText $command -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null              
            }
            "Sleep while waiting for OS Config script to run"
            Start-Sleep -Seconds 300
            $completed = $true
            $message = "OS Config successful"
        }
        catch {
            $completed = $false
            $message = "Failed to execute OS Config script, reason: " + ($Error[0].exception).Message
        }
    }
    else {
        $completed = $false
        $message = "Failed to connect to vCenter $($buildSpecs.vc_node) using account $($cliCreds.Username)"
    }

    $task = "" | Select-Object Succeeded, Message
    $task.Succeeded = $completed
    $task.Message = $message
    $task
}

Function SGTLinuxOsConfigs {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]$buildSpecs
    )

    $cliCreds = Import-Clixml -Path $script:cliCredsPath
    $vCenterSuccess = vCenter_Connection -vCenterName $buildSpecs.vc_node -credentials $cliCreds
    $vmName = $buildSpecs.vm_name

    if ($vCenterSuccess) {    
        try {
            Invoke-VMScript -VM $vmName -ScriptText "wget --no-check-certificate -qO - https://***********/cloud/deployment/PostDeployConfig8.sh|bash" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null    
            Start-Sleep -Seconds 300
            "Sleep while waiting for OS Config script to run"
            $completed = $true
            $message = "OS Config successful"
        }
        catch {
            $completed = $false
            $message = "Failed to execute OS Config script, reason: " + ($Error[0].exception).Message
        }
    }
    else {
        $completed = $false
        $message = "Failed to connect to vCenter $($buildSpecs.vc_node) using account $($cliCreds.Username)"
    }

    $task = "" | Select-Object Succeeded, Message
    $task.Succeeded = $completed
    $task.Message = $message
    $task
}

Function vCenter_Connection {
    param(
        [Parameter(Mandatory = $true)]$vCenterName,
        [Parameter(Mandatory = $true)][System.Management.Automation.PSCredential]$credentials
    )

    Try {
        Connect-VIServer -Server $vCenterName -Credential $credentials
        $vCenterConnected = $true
    }
    catch {
        $vCenterConnected = $false
    }

    $vCenterConnected
}

function Start-ADDMScan {
    <#
    .SYNOPSIS
        Starts ADDM discovery scan for a server
    .PARAMETER SpecInfo
        Server specification information object
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$SpecInfo
    )

    try {
        Write-Verbose "Starting ADDM scan for $($SpecInfo.vm_name)"

        $inADDM = get_addm -vm $SpecInfo.vm_name -ErrorAction Stop
        if ($inADDM.State -notmatch "Finished") {
            try {
                $result = set_addm -vmIP $SpecInfo.ip_address -JobID $SpecInfo.request_jobid -waitForScan $false -ErrorAction Stop
                Write-Verbose "ADDM scan initiated successfully"
            }
            catch {
                Write-Warning "Failed to start ADDM scan: $($_.Exception.Message)"
                $result = $false
            }
        }
        else {
            Write-Verbose "ADDM scan already completed"
            $result = $true
        }
    }
    catch {
        Write-Error "ADDM scan failed: $($_.Exception.Message)"
        $result = $false
    }

    return $result
}

# Main execution
Start-ConfigManagement
