# Add S3 Permissions to ImageBuilder IAM Role
# This script adds the necessary S3 permissions for the copy-s3-applications component

param(
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [string]$RoleName = "EC2ImageBuilderInstanceRole",
    
    [Parameter(Mandatory=$false)]
    [string]$S3Bucket = "sgt-imagebuilder",
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun = $false
)

# Function to write log messages
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message"
}

# S3 policy for the ImageBuilder role
$s3Policy = @{
    Version = "2012-10-17"
    Statement = @(
        @{
            Effect = "Allow"
            Action = @(
                "s3:GetObject",
                "s3:ListBucket"
            )
            Resource = @(
                "arn:aws:s3:::$S3Bucket",
                "arn:aws:s3:::$S3Bucket/*"
            )
        }
    )
} | ConvertTo-Json -Depth 10

Write-Log "Adding S3 permissions to ImageBuilder IAM role..." "INFO"
Write-Log "Role: $RoleName" "INFO"
Write-Log "S3 Bucket: $S3Bucket" "INFO"
Write-Log "Region: $Region" "INFO"

if ($DryRun) {
    Write-Log "DRY RUN - Policy that would be created:" "INFO"
    Write-Host $s3Policy
    exit 0
}

try {
    # Check if role exists
    $roleExists = aws iam get-role --role-name $RoleName --region $Region 2>$null
    if (!$roleExists) {
        Write-Log "IAM role $RoleName does not exist. Please create it first using deploy.ps1 -CreateIAMRole" "ERROR"
        exit 1
    }
    
    Write-Log "IAM role $RoleName found" "SUCCESS"
    
    # Create the policy
    $policyName = "ImageBuilderS3Access-$S3Bucket"
    Write-Log "Creating inline policy: $policyName" "INFO"
    
    # Save policy to temp file
    $tempPolicyFile = [System.IO.Path]::GetTempFileName()
    $s3Policy | Out-File -FilePath $tempPolicyFile -Encoding UTF8
    
    # Attach the policy to the role
    aws iam put-role-policy `
        --role-name $RoleName `
        --policy-name $policyName `
        --policy-document "file://$tempPolicyFile" `
        --region $Region
    
    if ($LASTEXITCODE -eq 0) {
        Write-Log "S3 policy attached successfully!" "SUCCESS"
        Write-Log "Policy Name: $policyName" "SUCCESS"
        
        # Clean up temp file
        Remove-Item $tempPolicyFile -Force
        
        # Verify the policy was attached
        Write-Log "Verifying policy attachment..." "INFO"
        $attachedPolicies = aws iam list-role-policies --role-name $RoleName --region $Region --output json | ConvertFrom-Json
        
        if ($attachedPolicies.PolicyNames -contains $policyName) {
            Write-Log "✓ Policy verification successful" "SUCCESS"
        } else {
            Write-Log "⚠ Policy attachment could not be verified" "WARNING"
        }
        
        Write-Log "" "INFO"
        Write-Log "Next steps:" "INFO"
        Write-Log "1. Deploy the copy-s3-applications component:" "INFO"
        Write-Log "   .\deploy-s3-applications-component.ps1 -Region $Region" "INFO"
        Write-Log "2. Add the component to your ImageBuilder recipe" "INFO"
        Write-Log "3. Build your AMI" "INFO"
        
    } else {
        Write-Log "Failed to attach S3 policy to role" "ERROR"
        Remove-Item $tempPolicyFile -Force
        exit 1
    }
    
} catch {
    Write-Log "Error: $($_.Exception.Message)" "ERROR"
    if (Test-Path $tempPolicyFile) {
        Remove-Item $tempPolicyFile -Force
    }
    exit 1
}

Write-Log "S3 permissions setup completed!" "SUCCESS"
