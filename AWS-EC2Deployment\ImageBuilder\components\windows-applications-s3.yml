# AWS ImageBuilder Component: Copy Applications from S3 (Fixed Version)
# Author: <PERSON><PERSON>
# Date: 2025-09-29
# Description: Copy applications from S3 using AWS CLI v2 (syntax corrected)

name: windows-applications-s3
description: Copy application files from S3 bucket using AWS CLI v2 (fixed syntax)
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: CopyApplicationsFromS3
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Starting S3 applications copy process..."

              # Configuration
              $s3Bucket = "sgt-imagebuilder"
              $s3Prefix = "windows/applications/"
              $targetDir = "C:\Temp\ServerInstalls\Applications"
              $region = "af-south-1"

              Write-Host "Configuration:"
              Write-Host "  S3 Bucket: $s3Bucket"
              Write-Host "  S3 Prefix: $s3Prefix"
              Write-Host "  Target Directory: $targetDir"
              Write-Host "  Region: $region"

              # Create target directory
              if (!(Test-Path $targetDir)) {
                  New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
                  Write-Host "Created directory: $targetDir"
              } else {
                  Write-Host "Directory already exists: $targetDir"
              }

              # Set AWS environment variables
              $env:AWS_DEFAULT_REGION = $region
              $env:AWS_DEFAULT_OUTPUT = "json"

              # Find AWS CLI with comprehensive search
              Write-Host "Locating AWS CLI..."
              $awsCommand = $null

              # Add common AWS CLI paths to PATH first
              $commonPaths = @(
                  "C:\Program Files\Amazon\AWSCLIV2",
                  "C:\Program Files (x86)\Amazon\AWSCLIV2",
                  "C:\ProgramData\Amazon\AWSCLIV2",
                  "C:\Windows\System32"
              )

              foreach ($path in $commonPaths) {
                  if (Test-Path $path) {
                      $env:PATH = "$env:PATH;$path"
                      Write-Host "Added to PATH: $path"
                  }
              }

              # Method 1: Try direct call with updated PATH
              try {
                  $result = aws --version 2>&1
                  if ($LASTEXITCODE -eq 0) {
                      Write-Host "AWS CLI found via PATH: $result"
                      $awsCommand = "aws"
                  }
              } catch {
                  Write-Host "Method 1 failed: Direct AWS CLI call"
              }

              # Method 2: Try specific executable paths
              if (-not $awsCommand) {
                  $awsPaths = @(
                      "C:\Program Files\Amazon\AWSCLIV2\aws.exe",
                      "C:\Program Files (x86)\Amazon\AWSCLIV2\aws.exe",
                      "C:\ProgramData\Amazon\AWSCLIV2\aws.exe"
                  )

                  foreach ($awsPath in $awsPaths) {
                      if (Test-Path $awsPath) {
                          try {
                              $result = & "$awsPath" --version 2>&1
                              if ($LASTEXITCODE -eq 0) {
                                  Write-Host "AWS CLI found at: $awsPath"
                                  Write-Host "Version: $result"
                                  $awsCommand = "`"$awsPath`""
                                  break
                              }
                          } catch {
                              Write-Host "Failed to execute: $awsPath"
                          }
                      }
                  }
              }

              # Method 3: Use Start-Process approach
              if (-not $awsCommand) {
                  try {
                      $proc = Start-Process -FilePath "aws" -ArgumentList "--version" -Wait -PassThru -NoNewWindow -RedirectStandardOutput "aws_version.txt" -RedirectStandardError "aws_error.txt" -ErrorAction Stop
                      if ($proc.ExitCode -eq 0 -and (Test-Path "aws_version.txt")) {
                          $version = Get-Content "aws_version.txt" -Raw
                          Write-Host "AWS CLI found via Start-Process: $version"
                          $awsCommand = "aws"
                          Remove-Item "aws_version.txt" -Force -ErrorAction SilentlyContinue
                      }
                      Remove-Item "aws_error.txt" -Force -ErrorAction SilentlyContinue
                  } catch {
                      Write-Host "Method 3 failed: Start-Process approach"
                  }
              }

              # Method 4: Use Invoke-Expression with cmd
              if (-not $awsCommand) {
                  try {
                      $result = Invoke-Expression "cmd /c 'aws --version 2>&1'"
                      if ($LASTEXITCODE -eq 0) {
                          Write-Host "AWS CLI found via Invoke-Expression: $result"
                          $awsCommand = "cmd /c aws"
                      }
                  } catch {
                      Write-Host "Method 4 failed: Invoke-Expression with cmd"
                  }
              }

              if (-not $awsCommand) {
                  Write-Host "AWS CLI not found using any method. Checking environment..."
                  Write-Host "PATH: $env:PATH"
                  Write-Host "Checking if AWS PowerShell modules are available..."

                  # Try AWS PowerShell as fallback
                  try {
                      Import-Module AWSPowerShell.NetCore -ErrorAction Stop
                      Write-Host "AWS PowerShell module found - will use PowerShell cmdlets"
                      $awsCommand = "POWERSHELL"
                  } catch {
                      try {
                          Import-Module AWSPowerShell -ErrorAction Stop
                          Write-Host "AWS PowerShell module found - will use PowerShell cmdlets"
                          $awsCommand = "POWERSHELL"
                      } catch {
                          Write-Error "Neither AWS CLI nor AWS PowerShell modules are available. Cannot proceed."
                          exit 1
                      }
                  }
              }

              # Test S3 access
              Write-Host "Testing S3 bucket access..."
              try {
                  if ($awsCommand -like "*cmd.exe*") {
                      $listResult = cmd.exe /c "aws s3 ls s3://$s3Bucket/$s3Prefix --region $region" 2>&1
                  } else {
                      $listResult = & $awsCommand s3 ls "s3://$s3Bucket/$s3Prefix" --region $region 2>&1
                  }
                  
                  if ($LASTEXITCODE -eq 0) {
                      Write-Host "S3 bucket access confirmed"
                      if ($listResult) {
                          Write-Host "Files found:"
                          $listResult | ForEach-Object { Write-Host "  $_" }
                      } else {
                          Write-Host "S3 prefix appears to be empty"
                      }
                  } else {
                      Write-Warning "Could not list S3 contents, continuing with sync..."
                  }
              } catch {
                  Write-Warning "Error testing S3 access, continuing with sync..."
              }

              # Perform S3 sync
              Write-Host "Starting S3 sync operation..."
              try {
                  if ($awsCommand -like "*cmd.exe*") {
                      $syncResult = cmd.exe /c "aws s3 sync s3://$s3Bucket/$s3Prefix $targetDir --region $region" 2>&1
                  } else {
                      $syncResult = & $awsCommand s3 sync "s3://$s3Bucket/$s3Prefix" "$targetDir" --region $region 2>&1
                  }
                  
                  if ($LASTEXITCODE -eq 0) {
                      Write-Host "S3 sync completed successfully"
                      if ($syncResult) {
                          Write-Host "Sync output:"
                          $syncResult | ForEach-Object { Write-Host "  $_" }
                      } else {
                          Write-Host "No files copied (source empty or files up to date)"
                      }
                  } else {
                      Write-Host "S3 sync exit code: $LASTEXITCODE"
                      if ($syncResult) {
                          $syncOutput = $syncResult -join " "
                          if ($syncOutput -like "*does not exist*" -or $syncOutput -like "*NoSuchKey*") {
                              Write-Host "Source appears to be empty, continuing..."
                          } else {
                              Write-Error "S3 sync failed: $syncResult"
                              exit 1
                          }
                      }
                  }
              } catch {
                  Write-Error "Error during S3 sync: $($_.Exception.Message)"
                  exit 1
              }

              # Verify results
              Write-Host "Verifying copy results..."
              if (Test-Path $targetDir) {
                  $items = Get-ChildItem -Path $targetDir -Recurse -ErrorAction SilentlyContinue
                  if ($items -and $items.Count -gt 0) {
                      Write-Host "Applications directory contains $($items.Count) items:"
                      foreach ($item in $items | Select-Object -First 5) {
                          $relativePath = $item.FullName.Replace($targetDir, "").TrimStart('\')
                          $size = if ($item.PSIsContainer) { "[DIR]" } else { "$([math]::Round($item.Length / 1KB, 2)) KB" }
                          Write-Host "  $relativePath ($size)"
                      }
                      if ($items.Count -gt 5) {
                          Write-Host "  ... and $($items.Count - 5) more items"
                      }
                  } else {
                      Write-Host "Applications directory is empty (normal if S3 source is empty)"
                  }
              }

              Write-Host "S3 applications copy operation completed successfully"
