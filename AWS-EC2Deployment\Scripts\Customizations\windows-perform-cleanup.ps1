<powershell>
# -----------------------------------------------------------------------------
# EC2 Image Builder - Windows Image Sanitization Script
#
# This script performs a series of cleanup tasks and generalizes the system
# using Sysprep, preparing it to be used as a golden AMI.
#
# Usage:
# Add this script to an Image Builder component and use it in your build phase.
# -----------------------------------------------------------------------------

Write-Host "Starting Windows image sanitization process..."

# ---- Step 1: General System Cleanup ----
# Clean up temporary files, logs, and other build artifacts.
Write-Host "Performing general system cleanup..."

# Clear user and system temporary folders
Remove-Item -Path "$Env:TEMP\*" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "C:\Windows\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue

# Clear the Recycle Bin
Clear-RecycleBin -Force -ErrorAction SilentlyContinue

# Clean up Windows Update cache
Remove-Item -Path "C:\Windows\SoftwareDistribution\Download\*" -Recurse -Force -ErrorAction SilentlyContinue

# Clear Windows Event Logs
Clear-EventLog -LogName Application, Security, System, Setup -ErrorAction SilentlyContinue

# Stop the EC2 service and clear its logs
Stop-Service -Name "EC2Config" -ErrorAction SilentlyContinue
Stop-Service -Name "EC2Launch" -ErrorAction SilentlyContinue
Remove-Item -Path "C:\ProgramData\Amazon\*" -Recurse -Force -ErrorAction SilentlyContinue

# ---- Step 2: Run DISM and SFC ----
# Use DISM to clean up the WinSxS folder and SFC to check system file integrity.
Write-Host "Running DISM for component store cleanup..."

# Clean up superseded components in the WinSxS folder
# The /ResetBase switch is more aggressive and should be used with caution, but is appropriate for AMI creation.
Start-Process -FilePath "dism.exe" -ArgumentList "/online /Cleanup-Image /StartComponentCleanup /ResetBase" -Wait -NoNewWindow

Write-Host "Running System File Checker (SFC) to verify system files..."
Start-Process -FilePath "sfc.exe" -ArgumentList "/scannow" -Wait -NoNewWindow

# ---- Step 3: Delete the Local Administrator Profile ----
# Remove the profile for the local Administrator account to ensure a clean state.
# A new profile is created for the `Administrator` user on the first boot.
Write-Host "Deleting the local Administrator profile..."
$adminProfilePath = "C:\Users\<USER>\ProgramData\Amazon\EC2-Windows\Launch\Settings\EC2LaunchSettings.exe"
if (Test-Path $ec2LaunchSettingsPath) {
    # Shutdown with Sysprep using EC2LaunchSettings
    # Use -NoNewWindow to ensure it's executed within the same process context.
    Start-Process -FilePath $ec2LaunchSettingsPath -ArgumentList "Sysprep" -Wait -NoNewWindow
} else {
    # Fallback to the traditional Sysprep command if EC2LaunchSettings is not found
    # The /quit argument is for EC2Config compatibility and can be omitted.
    # The /oobe, /generalize, and /shutdown switches are the critical part.
    $sysprepPath = "C:\Windows\System32\Sysprep\Sysprep.exe"
    if (Test-Path $sysprepPath) {
        Start-Process -FilePath $sysprepPath -ArgumentList "/oobe /generalize /shutdown" -Wait -NoNewWindow
    } else {
        Write-Error "Could not find Sysprep.exe or EC2LaunchSettings.exe."
        Exit 1
    }
}

# ---- Step 5: Trigger the built-in Image Builder cleanup (if needed) ----
# Create the `perform_cleanup` file to tell Image Builder to run its internal cleanup script.
# This step is only required if you use custom User data and want the default cleanup to execute.
Write-Host "Creating 'perform_cleanup' file to trigger Image Builder's native cleanup."
New-Item -Path "C:\" -Name "perform_cleanup" -ItemType "file"

Write-Host "Sanitization script complete. The instance will now shut down."

# The Sysprep command with `/shutdown` will handle the instance shutdown.
</powershell>