# Example AWS ImageBuilder Recipe with S3 Applications Copy
# This recipe demonstrates how to include the copy-s3-applications component

name: WindowsServer2022WithS3Applications
description: Windows Server 2022 base image with applications copied from S3
schemaVersion: 1.0
version: 1.0.0

# Base image - Windows Server 2022 (automatically uses latest)
parentImage: Windows_Server-2022-English-Full-Base

# Build and test components
components:
# AWS managed component for Windows updates
- name: update-windows
  parameters:
  - name: exclude
    value:
    - "KB5005463" # Example: exclude specific updates if needed
  - name: include
    value: []

# Install .NET Framework 4.8 (if needed for your applications)
- name: install-dotnet48
  parameters: []

# Copy applications from S3 bucket (uses defaults: sgt-imagebuilder bucket, af-south-1 region)
- name: windows-applications-s3-v2
  parameters: []

# Additional components can be added here
# - name: windows-tools-bginfo
#   parameters: []

# Test phase - verify the applications were copied
- name: test-phase
  parameters: []

# Working directory for the build
workingDirectory: /tmp

# Additional metadata
tags:
  Environment: Production
  Component: S3Applications
  CreatedBy: ImageBuilder
