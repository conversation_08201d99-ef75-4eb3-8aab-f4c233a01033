# ImageBuilder User Data Template
# Author: <PERSON><PERSON>
# Date: 2025-09-26
# Description: This script is used during ImageBuilder recipe execution to prepare the base AMI
# It should NOT contain business-specific configurations like admin groups or domain joining

<powershell>
# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope LocalMachine -Force

# Create scripts directory structure for future deployment use
$directories = @(
    "C:\Temp\ServerInstalls",
    "C:\Temp\ServerInstalls\Logs",
    "C:\Temp\ServerInstalls\Config",
    "C:\Temp\ServerInstalls\Tools",
    "C:\Temp\ServerInstalls\Applications"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# Create log file
$logFile = "C:\Temp\ServerInstalls\imagebuilder-prep.log"
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

Write-Log "Starting ImageBuilder preparation script..."

# Enable WinRM for remote management
Write-Log "Enabling WinRM for remote management"
Enable-PSRemoting -Force -SkipNetworkProfileCheck

# Configure WinRM settings
Write-Log "Configuring WinRM settings"
winrm quickconfig -q
winrm set winrm/config/winrs '@{MaxMemoryPerShellMB="512"}'
winrm set winrm/config '@{MaxTimeoutms="1800000"}'
winrm set winrm/config/service '@{AllowUnencrypted="false"}'
winrm set winrm/config/service/auth '@{Basic="true"}'

# Create self-signed certificate for HTTPS
Write-Log "Creating self-signed certificate for WinRM HTTPS"
$cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
winrm create winrm/config/Listener?Address=*+Transport=HTTPS "@{Hostname=`"localhost`";CertificateThumbprint=`"$($cert.Thumbprint)`"}"

# Configure firewall for WinRM HTTPS
Write-Log "Configuring firewall for WinRM HTTPS"
netsh advfirewall firewall add rule name="WinRM-HTTPS" dir=in localport=5986 protocol=TCP action=allow

# Restart WinRM service
Write-Log "Restarting WinRM service"
Restart-Service winrm

Write-Log "WinRM configuration completed"

# Install AWS CLI and SSM Agent updates
Write-Log "Updating AWS SSM Agent..."
try {
    $ssmService = Get-Service -Name "AmazonSSMAgent" -ErrorAction SilentlyContinue
    if ($ssmService) {
        Restart-Service -Name "AmazonSSMAgent" -Force
        Write-Log "SSM Agent restarted"
    }
} catch {
    Write-Log "Could not restart SSM Agent: $($_.Exception.Message)" "WARNING"
}

# Configure Windows Error Reporting for AWS
Write-Log "Configuring Windows Error Reporting..."
try {
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting" -Name "Disabled" -Value 1
    Write-Log "Windows Error Reporting disabled"
} catch {
    Write-Log "Could not configure Windows Error Reporting: $($_.Exception.Message)" "WARNING"
}

# Set timezone to Africa/Johannesburg for local consistency
Write-Log "Setting timezone to Africa/Johannesburg..."
try {
    Set-TimeZone -Id "South Africa Standard Time"
    Write-Log "Timezone set to Africa/Johannesburg (GMT+2)"
} catch {
    Write-Log "Could not set timezone: $($_.Exception.Message)" "WARNING"
}

# Configure Windows Defender exclusions for ImageBuilder
Write-Log "Configuring Windows Defender exclusions..."
try {
    Add-MpPreference -ExclusionPath "C:\Temp\ServerInstalls" -ErrorAction SilentlyContinue
    Add-MpPreference -ExclusionPath "C:\Windows\Temp" -ErrorAction SilentlyContinue
    Write-Log "Windows Defender exclusions configured"
} catch {
    Write-Log "Could not configure Windows Defender exclusions: $($_.Exception.Message)" "WARNING"
}

# Enable long path support for modern applications
Write-Log "Enabling long path support..."
try {
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "LongPathsEnabled" -Value 1
    Write-Log "Long path support enabled"
} catch {
    Write-Log "Could not enable long path support: $($_.Exception.Message)" "WARNING"
}

try {
    # ImageBuilder preparation tasks
    Write-Log "Starting ImageBuilder preparation tasks..."

    # Enable RDP with Network Level Authentication
    Write-Log "Enabling RDP with Network Level Authentication..."
    try {
        Set-ItemProperty -Path "HKLM:\System\CurrentControlSet\Control\Terminal Server" -Name "fDenyTSConnections" -Value 0
        Set-ItemProperty -Path "HKLM:\System\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "UserAuthentication" -Value 1
        Enable-NetFirewallRule -DisplayGroup "Remote Desktop"
        Write-Log "RDP enabled with NLA"
    } catch {
        Write-Log "Could not configure RDP: $($_.Exception.Message)" "WARNING"
    }

    # Turn on DEP for essential Windows programs and services
    Write-Log "Configuring Data Execution Prevention..."
    try {
        bcdedit /set nx OptIn
        Write-Log "DEP configured for essential Windows programs and services"
    } catch {
        Write-Log "Could not configure DEP: $($_.Exception.Message)" "WARNING"
    }

    # Configure RecycleBin settings for C: drive
    Write-Log "Configuring RecycleBin settings..."
    try {
        Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\BitBucket\Volume\{C:}" -Name "NukeOnDelete" -Value 1 -ErrorAction SilentlyContinue
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\BitBucket\Volume\{C:}" -Name "NukeOnDelete" -Value 1 -ErrorAction SilentlyContinue
        Write-Log "RecycleBin configured to not move files to recycle bin for C:"
    } catch {
        Write-Log "Could not configure RecycleBin settings: $($_.Exception.Message)" "WARNING"
    }

    # Disable IPv6 (uncheck but not remove)
    Write-Log "Disabling IPv6..."
    try {
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters" -Name "DisabledComponents" -Value 0xFF
        Write-Log "IPv6 disabled"
    } catch {
        Write-Log "Could not disable IPv6: $($_.Exception.Message)" "WARNING"
    }

    # Set high performance power plan
    try {
        powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
        Write-Log "Set high performance power plan"
    } catch {
        Write-Log "Could not set power plan: $($_.Exception.Message)" "WARNING"
    }

    # Disable unnecessary services for server use
    $servicesToDisable = @(
        "Themes",
        "TabletInputService",
        "WSearch"  # Windows Search (can be re-enabled if needed)
    )

    foreach ($service in $servicesToDisable) {
        try {
            Set-Service -Name $service -StartupType Disabled -ErrorAction SilentlyContinue
            Write-Log "Disabled service: $service"
        } catch {
            Write-Log "Could not disable service $service`: $($_.Exception.Message)" "WARNING"
        }
    }

    # Configure page file for optimal performance
    Write-Log "Configuring system-managed page file..."
    try {
        $cs = Get-WmiObject -Class Win32_ComputerSystem
        $cs.AutomaticManagedPagefile = $true
        $cs.Put() | Out-Null

        # Remove existing page files first
        $pf = Get-WmiObject -Class Win32_PageFileSetting
        if ($pf) {
            $pf.Delete()
        }

        Write-Log "Configured system-managed page file"
    } catch {
        Write-Log "Could not configure page file: $($_.Exception.Message)" "WARNING"
    }

    # Configure disk allocation unit size to 4K for non-OS drives
    Write-Log "Configuring disk allocation unit size..."
    try {
        $disks = Get-Disk | Where-Object { $_.Number -ne 0 -and $_.PartitionStyle -ne "GPT" }
        foreach ($disk in $disks) {
            # Convert to GPT if not already
            if ($disk.PartitionStyle -eq "MBR") {
                Clear-Disk -Number $disk.Number -RemoveData -Confirm:$false
                Initialize-Disk -Number $disk.Number -PartitionStyle GPT
                Write-Log "Converted disk $($disk.Number) to GPT"
            }
        }
        Write-Log "Non-OS drives configured for GPT with 4K allocation unit size"
    } catch {
        Write-Log "Could not configure disk settings: $($_.Exception.Message)" "WARNING"
    }

    # Create image information file for deployment scripts to reference
    Write-Log "Creating image information file..."

    $imageInfo = @{
        ImageBuildDate = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        ImageBuilderVersion = "1.0"
        BaseAMI = "Windows Server 2022"
        PreparedBy = "ImageBuilder"
        WinRMEnabled = $true
        OptimizedForServer = $true
        ReadyForDeployment = $true
        PowerPlan = "High Performance"
        PageFile = "System Managed"
        ServicesOptimized = $true
        Timezone = "South Africa Standard Time"
    }

    $imageInfoPath = "C:\Temp\ServerInstalls\image-info.json"
    $imageInfo | ConvertTo-Json -Depth 10 | Out-File -FilePath $imageInfoPath -Encoding UTF8
    Write-Log "Image information saved to: $imageInfoPath"

    # Create a placeholder for deployment scripts to reference
    Write-Log "Creating deployment script placeholder..."

    $deploymentPlaceholder = @"
# This file is created during ImageBuilder preparation
# Deployment-specific configurations should be applied via EC2 user-data scripts
#
# Available directories:
# - C:\Temp\ServerInstalls\Logs    - For deployment logs
# - C:\Temp\ServerInstalls\Config  - For configuration files
# - C:\Temp\ServerInstalls\Tools   - For deployment tools
#
# Image prepared on: $(Get-Date)
# Ready for business-specific deployment
"@

    $deploymentPlaceholder | Out-File -FilePath "C:\Temp\ServerInstalls\README-Deployment.txt" -Encoding UTF8
    Write-Log "Deployment placeholder created"

    # Final ImageBuilder preparation steps
    Write-Log "Completing ImageBuilder preparation..."

    # Ensure all required directories exist
    $requiredDirs = @("C:\Temp\ServerInstalls", "C:\Temp\ServerInstalls\Logs", "C:\Temp\ServerInstalls\Config", "C:\Temp\ServerInstalls\Tools")
    foreach ($dir in $requiredDirs) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Log "Created directory: $dir"
        }
    }

    # Set appropriate permissions on Scripts directory
    try {
        $acl = Get-Acl "C:\Temp\ServerInstalls"
        $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Administrators", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
        $acl.SetAccessRule($accessRule)
        Set-Acl "C:\Temp\ServerInstalls" $acl
        Write-Log "Set permissions on C:\Temp\ServerInstalls directory"
    } catch {
        Write-Log "Could not set permissions on Scripts directory: $($_.Exception.Message)" "WARNING"
    }

    # Validate ImageBuilder readiness
    Write-Log "Validating ImageBuilder readiness..."
    $validationResults = @{
        WinRMHTTPS = (Get-NetFirewallRule -DisplayName "WinRM-HTTPS" -ErrorAction SilentlyContinue) -ne $null
        DirectoriesCreated = (Test-Path "C:\Temp\ServerInstalls\Logs") -and (Test-Path "C:\Temp\ServerInstalls\Config")
        SSMAgent = (Get-Service -Name "AmazonSSMAgent" -ErrorAction SilentlyContinue).Status -eq "Running"
        PowerPlan = (powercfg /getactivescheme) -match "High performance"
        Timezone = (Get-TimeZone).Id -eq "South Africa Standard Time"
    }

    $validationResults | ConvertTo-Json | Out-File -FilePath "C:\Temp\ServerInstalls\validation-results.json" -Encoding UTF8
    Write-Log "Validation results saved"

    # Clean up temporary files
    Write-Log "Cleaning up temporary files..."
    try {
        Remove-Item -Path "C:\Windows\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue
        Write-Log "Temporary files cleaned"
    } catch {
        Write-Log "Could not clean temporary files: $($_.Exception.Message)" "WARNING"
    }

    # Create cleanup file to ensure the cleanup and sysprep step triggers
    Write-Log "Creating 'perform_cleanup' file..."
    try {
        New-Item -Path "C:\" -Name "perform_cleanup" -ItemType "file"
        Write-Log "perform_cleanup file created"
    } catch {
        Write-Log "Could not create perform_cleanup file: $($_.Exception.Message)" "WARNING"
    }

    Write-Log "ImageBuilder preparation completed successfully"

} catch {
    Write-Log "ImageBuilder preparation failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.Exception.StackTrace)" "ERROR"
    exit 1
}

Write-Log "ImageBuilder user data script completed"
</powershell>

