# User-Data Script: Copy Applications from S3 at Runtime
# Author: <PERSON><PERSON>
# Date: 2025-09-29
# Description: Copy applications from S3 during instance startup (alternative to ImageBuilder component)

<powershell>
# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope LocalMachine -Force

# Function to write log messages
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    
    # Also write to log file
    $logFile = "C:\Temp\ServerInstalls\Logs\s3-applications-copy.log"
    if (!(Test-Path (Split-Path $logFile))) {
        New-Item -ItemType Directory -Path (Split-Path $logFile) -Force | Out-Null
    }
    Add-Content -Path $logFile -Value $logEntry
}

try {
    Write-Log "Starting S3 applications copy process..." "INFO"
    
    # Configuration
    $s3Bucket = "sgt-imagebuilder"
    $s3Prefix = "windows/applications/"
    $targetDir = "C:\Temp\ServerInstalls\Applications"
    $region = "af-south-1"
    
    # Get instance metadata for more specific targeting (optional)
    try {
        $instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id" -TimeoutSec 5
        $availabilityZone = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/availability-zone" -TimeoutSec 5
        Write-Log "Instance ID: $instanceId, AZ: $availabilityZone" "INFO"
        
        # Get instance tags (requires IAM permission: ec2:DescribeTags)
        try {
            $tags = aws ec2 describe-tags --filters "Name=resource-id,Values=$instanceId" --region $region --output json | ConvertFrom-Json
            $businessUnit = ($tags.Tags | Where-Object { $_.Key -eq "Business-Unit" }).Value
            $environment = ($tags.Tags | Where-Object { $_.Key -eq "Environment" }).Value
            $serverRole = ($tags.Tags | Where-Object { $_.Key -eq "Server-Role" }).Value
            
            if ($businessUnit) { Write-Log "Business Unit: $businessUnit" "INFO" }
            if ($environment) { Write-Log "Environment: $environment" "INFO" }
            if ($serverRole) { Write-Log "Server Role: $serverRole" "INFO" }
            
            # Optionally modify S3 path based on tags
            # Example: $s3Prefix = "windows/applications/$businessUnit/"
            
        } catch {
            Write-Log "Could not retrieve instance tags: $($_.Exception.Message)" "WARNING"
        }
    } catch {
        Write-Log "Could not retrieve instance metadata: $($_.Exception.Message)" "WARNING"
    }
    
    Write-Log "S3 Bucket: $s3Bucket" "INFO"
    Write-Log "S3 Prefix: $s3Prefix" "INFO"
    Write-Log "Target Directory: $targetDir" "INFO"
    Write-Log "Region: $region" "INFO"
    
    # Create target directory
    if (!(Test-Path $targetDir)) {
        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        Write-Log "Created directory: $targetDir" "INFO"
    } else {
        Write-Log "Directory already exists: $targetDir" "INFO"
    }
    
    # Check if AWS CLI is available
    try {
        $awsVersion = aws --version 2>&1
        Write-Log "AWS CLI Version: $awsVersion" "INFO"
    } catch {
        Write-Log "AWS CLI is not available or not in PATH" "ERROR"
        throw "AWS CLI not found"
    }
    
    # Test S3 bucket access
    Write-Log "Testing S3 bucket access..." "INFO"
    try {
        $bucketTest = aws s3 ls "s3://$s3Bucket/$s3Prefix" --region $region 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Log "Could not list S3 bucket contents: $bucketTest" "WARNING"
            # Check if it's a permissions issue or empty folder
            if ($bucketTest -like "*AccessDenied*") {
                throw "Access denied to S3 bucket. Check IAM permissions."
            } elseif ($bucketTest -like "*NoSuchBucket*") {
                throw "S3 bucket does not exist: $s3Bucket"
            } else {
                Write-Log "S3 folder may be empty, continuing..." "INFO"
            }
        } else {
            Write-Log "S3 bucket access confirmed" "INFO"
            Write-Log "Available files: $bucketTest" "INFO"
        }
    } catch {
        Write-Log "Error testing S3 access: $($_.Exception.Message)" "ERROR"
        throw
    }
    
    # Copy files from S3 to local directory
    Write-Log "Starting S3 sync operation..." "INFO"
    try {
        $syncCommand = "aws s3 sync `"s3://$s3Bucket/$s3Prefix`" `"$targetDir`" --region $region"
        Write-Log "Executing: $syncCommand" "INFO"
        
        $syncResult = Invoke-Expression $syncCommand 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "S3 sync completed successfully" "INFO"
            if ($syncResult) {
                Write-Log "Sync output: $syncResult" "INFO"
            } else {
                Write-Log "No files were copied (source may be empty or files already up to date)" "INFO"
            }
        } else {
            Write-Log "S3 sync completed with warnings or errors: $syncResult" "WARNING"
            # Don't fail if it's just because the folder is empty
            if ($syncResult -like "*does not exist*" -or $syncResult -like "*NoSuchKey*") {
                Write-Log "Source folder appears to be empty, continuing..." "INFO"
            } else {
                throw "S3 sync failed: $syncResult"
            }
        }
    } catch {
        Write-Log "Error during S3 sync: $($_.Exception.Message)" "ERROR"
        throw
    }
    
    # Verify and report results
    Write-Log "Verifying applications directory contents..." "INFO"
    if (Test-Path $targetDir) {
        $items = Get-ChildItem -Path $targetDir -Recurse
        if ($items.Count -gt 0) {
            Write-Log "Applications directory contains $($items.Count) items:" "INFO"
            foreach ($item in $items) {
                $relativePath = $item.FullName.Replace($targetDir, "").TrimStart('\')
                $size = if ($item.PSIsContainer) { "[DIR]" } else { "$([math]::Round($item.Length / 1KB, 2)) KB" }
                Write-Log "  $relativePath ($size)" "INFO"
            }
        } else {
            Write-Log "Applications directory is empty" "INFO"
        }
        
        # Set appropriate permissions for the directory
        try {
            $acl = Get-Acl $targetDir
            # Ensure SYSTEM and Administrators have full control
            $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("SYSTEM", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
            $acl.SetAccessRule($accessRule)
            $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Administrators", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
            $acl.SetAccessRule($accessRule)
            Set-Acl -Path $targetDir -AclObject $acl
            Write-Log "Directory permissions configured successfully" "INFO"
        } catch {
            Write-Log "Could not set directory permissions: $($_.Exception.Message)" "WARNING"
        }
    } else {
        throw "Applications directory was not created successfully"
    }
    
    Write-Log "S3 applications copy operation completed successfully" "SUCCESS"
    
} catch {
    Write-Log "S3 applications copy failed: $($_.Exception.Message)" "ERROR"
    # Don't exit with error code as this might prevent other user-data scripts from running
    # Instead, log the error and continue
    Write-Log "Continuing with instance startup despite S3 copy failure..." "WARNING"
}

Write-Log "S3 applications copy script completed" "INFO"
</powershell>
