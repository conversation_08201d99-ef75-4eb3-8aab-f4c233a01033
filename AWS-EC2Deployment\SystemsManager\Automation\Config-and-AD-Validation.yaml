schemaVersion: "0.3"
description: |-
  Configuration Loading and AD Object Creation Validation Runbook
  This runbook loads deployment configuration from S3, creates AD computer objects via webhook API,
  and validates the results. Useful for testing configuration and AD integration before full deployment.
assumeRole: '{{AutomationAssumeRole}}'
parameters:
  AutomationAssumeRole:
    default: ''
    description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf.
    type: String
  AssetOwner:
    description: (Required) Asset Owner (e.g., Sanlam, Santam, Retail Mass)
    type: String
  AppType:
    description: (Required) Application Type (e.g., Shared, MSSQL)
    type: String
  Client:
    description: (Required) Client identifier (e.g., SGT, SPF, SC, STM, etc.)
    type: String
  Environment:
    description: (Required) Environment (e.g., PRD, PPE, DEV)
    type: String
  OSVersion:
    description: (Required) OS Version (e.g., Windows Server 2022, Windows Server 2019)
    type: String
  S3ConfigBucket:
    description: (Required) S3 bucket containing configuration files
    type: String
  SecretsManagerSecretArn:
    description: (Required) Secrets Manager secret ARN containing domain credentials and webhook API details
    type: String
  ComputerName:
    description: (Required) Computer name for the AD object
    type: String
  Region:
    description: (Optional) AWS Region
    type: String
    default: 'af-south-1'
  TestMode:
    description: (Optional) Run in test mode - validates configuration and webhook connectivity without creating actual AD objects
    type: String
    default: 'false'
    allowedValues:
    - 'true'
    - 'false'

mainSteps:
- name: loadConfiguration
  action: aws:executeScript
  description: Load and validate deployment configuration from S3
  inputs:
    Runtime: PowerShell 7.4
    Handler: loadConfig
    Script: |-
      function loadConfig {
        param($AssetOwner, $AppType, $Client, $Environment, $OSVersion, $S3ConfigBucket, $Region)
        
        try {
          Write-Output "=== Configuration Loading Started ==="
          Write-Output "Asset Owner: $AssetOwner"
          Write-Output "App Type: $AppType"
          Write-Output "Client: $Client"
          Write-Output "Environment: $Environment"
          Write-Output "OS Version: $OSVersion"
          Write-Output "S3 Config Bucket: $S3ConfigBucket"
          Write-Output "Region: $Region"
          
          # Load base configuration
          $baseConfigKey = "configs/base_config.json"
          $tempFile = [System.IO.Path]::GetTempFileName()
          
          Write-Output "Downloading base configuration from: s3://$S3ConfigBucket/$baseConfigKey"
          aws s3 cp "s3://$S3ConfigBucket/$baseConfigKey" $tempFile --region $Region
          if ($LASTEXITCODE -ne 0) { throw "Failed to download base config from S3" }
          
          $baseConfig = Get-Content $tempFile -Raw | ConvertFrom-Json
          Remove-Item $tempFile -Force
          Write-Output "Base configuration loaded successfully"
          
          # Validate inputs against base configuration
          Write-Output "Validating input parameters..."
          if ($AssetOwner -notin $baseConfig.ASSET_OWNER) {
            throw "Invalid Asset Owner: $AssetOwner. Valid options: $($baseConfig.ASSET_OWNER -join ', ')"
          }
          Write-Output "Asset Owner validated: $AssetOwner"
          
          $assetConfig = $baseConfig.$AssetOwner
          if ($AppType -notin $assetConfig.APP_TYPE) { 
            throw "Invalid App Type: $AppType. Valid options for $AssetOwner: $($assetConfig.APP_TYPE -join ', ')" 
          }
          Write-Output "App Type validated: $AppType"
          
          if ($Environment -notin $assetConfig.ENV) { 
            throw "Invalid Environment: $Environment. Valid options: $($assetConfig.ENV -join ', ')" 
          }
          Write-Output "Environment validated: $Environment"
          
          if ($OSVersion -notin $assetConfig.OS_VERSION) { 
            throw "Invalid OS Version: $OSVersion. Valid options: $($assetConfig.OS_VERSION -join ', ')" 
          }
          Write-Output "OS Version validated: $OSVersion"
          
          if ($Client -notin $assetConfig.CLIENT) { 
            throw "Invalid Client: $Client. Valid options: $($assetConfig.CLIENT -join ', ')" 
          }
          Write-Output "Client validated: $Client"
          
          # Load specific configuration
          $specificConfigKey = "configs/${AssetOwner}_${AppType}_Config.json"
          $tempFile2 = [System.IO.Path]::GetTempFileName()
          
          Write-Output "Downloading specific configuration from: s3://$S3ConfigBucket/$specificConfigKey"
          aws s3 cp "s3://$S3ConfigBucket/$specificConfigKey" $tempFile2 --region $Region
          if ($LASTEXITCODE -ne 0) { 
            Write-Warning "Specific config not found, trying shared config..."
            $sharedConfigKey = "configs/${AssetOwner}_Shared_Config.json"
            aws s3 cp "s3://$S3ConfigBucket/$sharedConfigKey" $tempFile2 --region $Region
            if ($LASTEXITCODE -ne 0) { throw "Failed to download configuration files from S3" }
            Write-Output "Using shared configuration: $sharedConfigKey"
          } else {
            Write-Output "Using specific configuration: $specificConfigKey"
          }
          
          $specificConfig = Get-Content $tempFile2 -Raw | ConvertFrom-Json
          Remove-Item $tempFile2 -Force
          
          # Extract configuration for the specific client and environment
          Write-Output "Extracting configuration for Client: $Client, Environment: $Environment"
          $clientConfig = $specificConfig.$Client
          if (-not $clientConfig) { 
            throw "Client '$Client' not found in configuration. Available clients: $($specificConfig.PSObject.Properties.Name -join ', ')" 
          }
          Write-Output "Client configuration found"
          
          $envConfig = $clientConfig.$Environment
          if (-not $envConfig) { 
            throw "Environment '$Environment' not found for client '$Client'. Available environments: $($clientConfig.PSObject.Properties.Name -join ', ')" 
          }
          Write-Output "Environment configuration found"
          
          $osConfig = $envConfig.OS_VERSIONS.$OSVersion
          if (-not $osConfig) { 
            throw "OS Version '$OSVersion' not found for environment '$Environment'. Available OS versions: $($envConfig.OS_VERSIONS.PSObject.Properties.Name -join ', ')" 
          }
          Write-Output "OS Version configuration found"
          
          # Build final configuration
          $finalConfig = @{
            AssetOwner = $AssetOwner
            AppType = $AppType
            Client = $Client
            Environment = $Environment
            OSVersion = $OSVersion
            Domain = $envConfig.DOMAIN
            BasePath = $envConfig.BASE_PATH
            LocalAdmins = $envConfig.LOCAL_ADM
            TargetOU = $osConfig.OU
            ConfigurationSource = if ($specificConfigKey) { $specificConfigKey } else { $sharedConfigKey }
            ValidationTimestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
          }
          
          Write-Output "=== Final Configuration ==="
          Write-Output "Domain: $($finalConfig.Domain)"
          Write-Output "Target OU: $($finalConfig.TargetOU)"
          Write-Output "Base Path: $($finalConfig.BasePath)"
          Write-Output "Local Admins: $($finalConfig.LocalAdmins -join ', ')"
          Write-Output "Configuration Source: $($finalConfig.ConfigurationSource)"
          Write-Output "=== Configuration Loading Completed Successfully ==="
          
          return ($finalConfig | ConvertTo-Json -Depth 5)
          
        } catch {
          Write-Error "Configuration loading failed: $($_.Exception.Message)"
          throw "Configuration loading failed: $($_.Exception.Message)"
        }
      }
    InputPayload:
      AssetOwner: '{{AssetOwner}}'
      AppType: '{{AppType}}'
      Client: '{{Client}}'
      Environment: '{{Environment}}'
      OSVersion: '{{OSVersion}}'
      S3ConfigBucket: '{{S3ConfigBucket}}'
      Region: '{{Region}}'
  timeoutSeconds: 300
  nextStep: createADObject
  outputs:
  - Name: ConfigurationResult
    Selector: $.Payload
    Type: String

- name: createADObject
  action: aws:executeScript
  description: Create AD computer object via on-premises webhook API with enhanced logging
  inputs:
    Runtime: PowerShell 7.4
    Handler: createADObject
    Script: |-
      function createADObject {
        param($ConfigJson, $ComputerName, $Region, $SecretsManagerSecretArn, $TestMode)

        try {
          Write-Output "=== AD Object Creation Started ==="
          
          # Load System.Web for URL encoding
          Add-Type -AssemblyName System.Web
          $config = $ConfigJson | ConvertFrom-Json

          Write-Output "Configuration loaded:"
          Write-Output "  Asset Owner: $($config.AssetOwner)"
          Write-Output "  Client: $($config.Client)"
          Write-Output "  App Type: $($config.AppType)"
          Write-Output "  Environment: $($config.Environment)"
          Write-Output "  Domain: $($config.Domain)"
          Write-Output "  Target OU: $($config.TargetOU)"
          Write-Output "  Test Mode: $TestMode"

          # Get webhook credentials from Secrets Manager
          Write-Output "Retrieving credentials from Secrets Manager..."
          $secretValue = Get-SECSecretValue -SecretId $SecretsManagerSecretArn -Region $Region
          $secretJson = $secretValue.SecretString | ConvertFrom-Json
          Write-Output "Secrets Manager credentials retrieved"

          # Validate computer name is provided
          if (-not $ComputerName -or $ComputerName -eq "") {
            throw "ComputerName parameter is required and cannot be empty"
          }
          
          Write-Output "Computer name provided: $ComputerName"

          # Generate job ID for tracking
          $jobId = "AWS-SSM-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
          Write-Output "Job ID generated: $jobId"

          # Create description
          $description = "AWS EC2 - $($config.AssetOwner) $($config.Client) $($config.AppType) $($config.Environment)"
          Write-Output "Description: $description"

          # Get webhook configuration from secrets
          $apiKey = $secretJson.webhookApiKey
          $webhookBaseUrl = $secretJson.webhookApiUrl
          $scriptName = "Create-IntraADObject.ps1"

          # Validate webhook configuration
          if (-not $apiKey -or -not $webhookBaseUrl) {
            throw "Webhook API key or URL not found in Secrets Manager. Please ensure 'webhookApiKey' and 'webhookApiUrl' are configured."
          }
          Write-Output "Webhook configuration validated"
          Write-Output "  Webhook URL: $webhookBaseUrl"
          Write-Output "  Script: $scriptName"

          # URL encode the parameters
          $paramString = "-jobId '$jobId' -objectName '$computerName' -objectDescription '$description' -vmOS 'Windows' -domain '$($config.Domain)' -ouPath '$($config.TargetOU)' -appType '$($config.AppType)'"
          $encodedParams = [System.Web.HttpUtility]::UrlEncode($paramString)

          # Build the webhook URL
          $webhookUrl = "$webhookBaseUrl?key=$apiKey&script=$scriptName&param=$encodedParams"

          Write-Output "=== Webhook API Call Details ==="
          Write-Output "Target Computer: $computerName"
          Write-Output "Target OU: $($config.TargetOU)"
          Write-Output "Domain: $($config.Domain)"
          Write-Output "Job ID: $jobId"
          Write-Output "Parameters: $paramString"

          if ($TestMode -eq 'true') {
            Write-Output "=== TEST MODE - Simulating API Call ==="
            Write-Output "Would call: $webhookBaseUrl"
            Write-Output "With parameters: $paramString"
            
            # Simulate successful response for testing
            $action = "TEST_MODE_SIMULATION"
            $apiSuccess = $true
            $apiMessage = "Test mode - API call simulated successfully"
            $actualOU = $config.TargetOU
            
            Write-Output "Test mode simulation completed"
          } else {
            # Call the webhook API
            Write-Output "Calling webhook API..."
            try {
              $response = Invoke-RestMethod -Uri $webhookUrl -Method GET -TimeoutSec 120
              Write-Output "Webhook API call completed"
              Write-Output "Raw webhook response: $($response | ConvertTo-Json -Depth 10)"

              # Parse the response structure
              if ($response.output) {
                $apiResult = $response.output

                if ($apiResult.success -eq $true -and $apiResult.status -eq "COMPLETED") {
                  Write-Output "=== AD Object Creation Successful ==="
                  Write-Output "  Computer Name: $($apiResult.data.objectName)"
                  Write-Output "  Domain: $($apiResult.data.Domain)"
                  Write-Output "  OU Created In: $($apiResult.data.ouCreatedIn)"
                  Write-Output "  Timestamp: $($apiResult.data.timeStamp)"
                  Write-Output "  Message: $($apiResult.message)"
                  $action = "CREATED_VIA_API"
                  $apiSuccess = $true
                  $apiMessage = $apiResult.message
                  $actualOU = $apiResult.data.ouCreatedIn
                } else {
                  Write-Warning "=== AD Object Creation Failed ==="
                  Write-Warning "  Status: $($apiResult.status)"
                  Write-Warning "  Message: $($apiResult.message)"
                  Write-Output "Using default OU for fallback"

                  # Get default OU from secrets manager or use fallback
                  $defaultOU = if ($secretJson.defaultTargetOU) {
                    $secretJson.defaultTargetOU
                  } else {
                    $config.TargetOU
                  }

                  Write-Output "Default OU for fallback: $defaultOU"
                  $action = "API_FAILED_USING_DEFAULT"
                  $apiSuccess = $false
                  $apiMessage = $apiResult.message
                  $actualOU = $defaultOU
                }
              } else {
                Write-Warning "=== Unexpected Response Format ==="
                Write-Output "Using default OU for fallback"

                # Get default OU from secrets manager or use fallback
                $defaultOU = if ($secretJson.defaultTargetOU) {
                  $secretJson.defaultTargetOU
                } else {
                  $config.TargetOU
                }

                Write-Output "Default OU for fallback: $defaultOU"
                $action = "API_FAILED_USING_DEFAULT"
                $apiSuccess = $false
                $apiMessage = "Unexpected response format"
                $actualOU = $defaultOU
              }
            } catch {
              # If webhook fails, use default OU for domain join
              Write-Warning "=== Webhook API Call Failed ==="
              Write-Warning "Error: $($_.Exception.Message)"
              Write-Output "Using default OU for fallback"

              # Get default OU from secrets manager or use fallback
              $defaultOU = if ($secretJson.defaultTargetOU) {
                $secretJson.defaultTargetOU
              } else {
                $config.TargetOU
              }

              Write-Output "Default OU for fallback: $defaultOU"
              $action = "API_FAILED_USING_DEFAULT"
              $apiSuccess = $false
              $apiMessage = $_.Exception.Message
              $actualOU = $defaultOU
            }
          }

          $result = @{
            ComputerName = $computerName
            Action = $action
            TargetOU = $config.TargetOU
            ActualOU = $actualOU
            Domain = $config.Domain
            JobId = $jobId
            WebhookUrl = $webhookBaseUrl
            Description = $description
            ApiSuccess = $apiSuccess
            ApiMessage = $apiMessage
            TestMode = $TestMode
            Timestamp = if ($apiResult.data.timeStamp) { $apiResult.data.timeStamp } else { Get-Date -Format "yyyy-MM-dd HH:mm:ss" }
            ConfigurationUsed = @{
              AssetOwner = $config.AssetOwner
              Client = $config.Client
              AppType = $config.AppType
              Environment = $config.Environment
              OSVersion = $config.OSVersion
            }
          }

          Write-Output "=== AD Object Creation Completed ==="
          Write-Output "Result: $($result | ConvertTo-Json -Depth 3)"

          return ($result | ConvertTo-Json -Depth 5)

        } catch {
          Write-Error "AD object creation failed: $($_.Exception.Message)"
          throw "AD object creation failed: $($_.Exception.Message)"
        }
      }
    InputPayload:
      ConfigJson: '{{loadConfiguration.ConfigurationResult}}'
      ComputerName: '{{ComputerName}}'
      Region: '{{Region}}'
      SecretsManagerSecretArn: '{{SecretsManagerSecretArn}}'
      TestMode: '{{TestMode}}'
  timeoutSeconds: 600
  nextStep: validateResults
  outputs:
  - Name: ADObjectResult
    Selector: $.Payload
    Type: String

- name: validateResults
  action: aws:executeScript
  description: Validate configuration loading and AD object creation results
  inputs:
    Runtime: PowerShell 7.4
    Handler: validateResults
    Script: |-
      function validateResults {
        param($ConfigJson, $ADObjectJson)

        try {
          Write-Output "=== Results Validation Started ==="
          
          $config = $ConfigJson | ConvertFrom-Json
          $adResult = $ADObjectJson | ConvertFrom-Json

          Write-Output "=== Configuration Validation ==="
          $configValidation = @{
            HasAssetOwner = -not [string]::IsNullOrEmpty($config.AssetOwner)
            HasClient = -not [string]::IsNullOrEmpty($config.Client)
            HasAppType = -not [string]::IsNullOrEmpty($config.AppType)
            HasEnvironment = -not [string]::IsNullOrEmpty($config.Environment)
            HasDomain = -not [string]::IsNullOrEmpty($config.Domain)
            HasTargetOU = -not [string]::IsNullOrEmpty($config.TargetOU)
            HasLocalAdmins = $config.LocalAdmins -and $config.LocalAdmins.Count -gt 0
          }

          foreach ($key in $configValidation.Keys) {
            $status = if ($configValidation[$key]) { "PASS" } else { "✗ FAIL" }
            Write-Output "  $key`: $status"
          }

          Write-Output "=== AD Object Creation Validation ==="
          $adValidation = @{
            HasComputerName = -not [string]::IsNullOrEmpty($adResult.ComputerName)
            HasAction = -not [string]::IsNullOrEmpty($adResult.Action)
            HasJobId = -not [string]::IsNullOrEmpty($adResult.JobId)
            HasTimestamp = -not [string]::IsNullOrEmpty($adResult.Timestamp)
            ApiSuccessful = $adResult.ApiSuccess -eq $true
            HasActualOU = -not [string]::IsNullOrEmpty($adResult.ActualOU)
            OUMatches = $adResult.TargetOU -eq $adResult.ActualOU
          }

          foreach ($key in $adValidation.Keys) {
            $status = if ($adValidation[$key]) { "PASS" } else { "✗ FAIL" }
            Write-Output "  $key`: $status"
          }

          Write-Output "=== Detailed Results Summary ==="
          Write-Output "Configuration:"
          Write-Output "  Asset Owner: $($config.AssetOwner)"
          Write-Output "  Client: $($config.Client)"
          Write-Output "  App Type: $($config.AppType)"
          Write-Output "  Environment: $($config.Environment)"
          Write-Output "  Domain: $($config.Domain)"
          Write-Output "  Target OU: $($config.TargetOU)"
          Write-Output "  Local Admins: $($config.LocalAdmins -join ', ')"

          Write-Output "AD Object Creation:"
          Write-Output "  Computer Name: $($adResult.ComputerName)"
          Write-Output "  Action Taken: $($adResult.Action)"
          Write-Output "  API Success: $($adResult.ApiSuccess)"
          Write-Output "  API Message: $($adResult.ApiMessage)"
          Write-Output "  Target OU: $($adResult.TargetOU)"
          Write-Output "  Actual OU: $($adResult.ActualOU)"
          Write-Output "  Job ID: $($adResult.JobId)"
          Write-Output "  Test Mode: $($adResult.TestMode)"
          Write-Output "  Timestamp: $($adResult.Timestamp)"

          # Overall validation status
          $configPassed = $configValidation.Values -notcontains $false
          $adPassed = $adValidation.Values -notcontains $false
          $overallPassed = $configPassed -and $adPassed

          $validationSummary = @{
            OverallStatus = if ($overallPassed) { "PASSED" } else { "FAILED" }
            ConfigurationValidation = if ($configPassed) { "PASSED" } else { "FAILED" }
            ADObjectValidation = if ($adPassed) { "PASSED" } else { "FAILED" }
            ConfigurationDetails = $configValidation
            ADObjectDetails = $adValidation
            Summary = @{
              ComputerName = $adResult.ComputerName
              Domain = $config.Domain
              TargetOU = $config.TargetOU
              ActualOU = $adResult.ActualOU
              ApiSuccess = $adResult.ApiSuccess
              TestMode = $adResult.TestMode
              ValidationTimestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            }
          }

          Write-Output "=== Final Validation Status ==="
          Write-Output "Overall Status: $($validationSummary.OverallStatus)"
          Write-Output "Configuration: $($validationSummary.ConfigurationValidation)"
          Write-Output "AD Object Creation: $($validationSummary.ADObjectValidation)"

          if ($overallPassed) {
            Write-Output "All validations passed - Ready for full deployment"
          } else {
            Write-Warning "✗ Some validations failed - Review configuration and AD setup"
          }

          Write-Output "=== Results Validation Completed ==="

          return ($validationSummary | ConvertTo-Json -Depth 5)

        } catch {
          Write-Error "Results validation failed: $($_.Exception.Message)"
          throw "Results validation failed: $($_.Exception.Message)"
        }
      }
    InputPayload:
      ConfigJson: '{{loadConfiguration.ConfigurationResult}}'
      ADObjectJson: '{{createADObject.ADObjectResult}}'
  timeoutSeconds: 300
  isEnd: true
  outputs:
  - Name: ValidationResult
    Selector: $.Payload
    Type: String
