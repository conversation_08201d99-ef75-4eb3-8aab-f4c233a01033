# AWS ImageBuilder Component: Ensure AWS CLI is Available
# Author: <PERSON><PERSON>
# Date: 2025-09-29
# Description: Ensure AWS CLI is installed and accessible

name: ensure-aws-cli
description: Ensure AWS CLI is installed and accessible for other components
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckAndInstallAWSCLI
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Checking AWS CLI availability..."
        
        # Function to test AWS CLI
        function Test-AWSCLI {
            try {
                $result = cmd /c "aws --version" 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "AWS CLI found: $result"
                    return $true
                }
            } catch {}
            
            try {
                $result = aws --version 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "AWS CLI found: $result"
                    return $true
                }
            } catch {}
            
            return $false
        }
        
        # Check if AWS CLI is already available
        if (Test-AWSCLI) {
            Write-Host "✓ AWS CLI is already available"
            exit 0
        }
        
        Write-Host "AWS CLI not found in PATH. Checking common installation locations..."
        
        # Common AWS CLI installation paths
        $awsCliPaths = @(
            "C:\Program Files\Amazon\AWSCLIV2\aws.exe",
            "C:\Program Files (x86)\Amazon\AWSCLIV2\aws.exe",
            "C:\ProgramData\Amazon\AWSCLIV2\aws.exe"
        )
        
        $foundPath = $null
        foreach ($path in $awsCliPaths) {
            if (Test-Path $path) {
                try {
                    $result = & $path --version 2>&1
                    if ($LASTEXITCODE -eq 0) {
                        Write-Host "Found AWS CLI at: $path"
                        Write-Host "Version: $result"
                        $foundPath = Split-Path $path -Parent
                        break
                    }
                } catch {}
            }
        }
        
        if ($foundPath) {
            # Add to system PATH
            Write-Host "Adding AWS CLI to system PATH..."
            $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
            if ($currentPath -notlike "*$foundPath*") {
                $newPath = "$currentPath;$foundPath"
                [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
                Write-Host "✓ Added $foundPath to system PATH"
            }
            
            # Update current session PATH
            $env:PATH = "$env:PATH;$foundPath"
            
            # Test again
            if (Test-AWSCLI) {
                Write-Host "✓ AWS CLI is now accessible"
                exit 0
            }
        }
        
        Write-Host "AWS CLI not found. Installing AWS CLI v2..."
        
        # Download and install AWS CLI v2
        $installerUrl = "https://awscli.amazonaws.com/AWSCLIV2.msi"
        $installerPath = "C:\temp\AWSCLIV2.msi"
        
        try {
            # Create temp directory
            if (!(Test-Path "C:\temp")) {
                New-Item -ItemType Directory -Path "C:\temp" -Force | Out-Null
            }
            
            Write-Host "Downloading AWS CLI installer..."
            Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath -UseBasicParsing
            
            if (!(Test-Path $installerPath)) {
                throw "Failed to download AWS CLI installer"
            }
            
            Write-Host "Installing AWS CLI..."
            $installProcess = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$installerPath`" /quiet /norestart" -Wait -PassThru -NoNewWindow
            
            if ($installProcess.ExitCode -eq 0) {
                Write-Host "✓ AWS CLI installation completed"
                
                # Clean up installer
                Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
                
                # Add to PATH
                $awsCliPath = "C:\Program Files\Amazon\AWSCLIV2"
                if (Test-Path $awsCliPath) {
                    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
                    if ($currentPath -notlike "*$awsCliPath*") {
                        $newPath = "$currentPath;$awsCliPath"
                        [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
                    }
                    $env:PATH = "$env:PATH;$awsCliPath"
                }
                
                # Final test
                if (Test-AWSCLI) {
                    Write-Host "✓ AWS CLI installation successful and verified"
                } else {
                    Write-Warning "AWS CLI was installed but is not accessible. May require system restart."
                }
                
            } else {
                throw "AWS CLI installation failed with exit code: $($installProcess.ExitCode)"
            }
            
        } catch {
            Write-Error "Failed to install AWS CLI: $($_.Exception.Message)"
            Write-Host "ImageBuilder instances should have AWS CLI pre-installed."
            Write-Host "This may indicate an issue with the base AMI or network connectivity."
            exit 1
        }
